* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 - 亮色主题（与uTools标签栏颜色完全一致） */
:root {
    --bg-primary: #f4f4f4;
    --bg-secondary: #f4f4f4;
    --bg-tertiary: #eeeeee;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-tertiary: #999999;
    --border-primary: #e0e0e0;
    --border-secondary: #cccccc;
    --accent-primary: #f4f4f4;
    --accent-secondary: #333333;
    --accent-hover: #eeeeee;
    --primary-btn-bg: #e8e8e8;
    --primary-btn-text: #333333;
    --primary-btn-hover: #dddddd;
    --shadow-light: rgba(0, 0, 0, 0.06);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.15);
    --success-bg: rgba(34, 197, 94, 0.1);
    --success-text: #16a34a;
    --warning-bg: rgba(251, 191, 36, 0.1);
    --warning-text: #d97706;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --info-bg: rgba(59, 130, 246, 0.1);
    --info-text: #2563eb;
    --error-bg: rgba(239, 68, 68, 0.1);
    --error-text: #dc2626;

    /* 强调色 - 用于选中状态等 */
    --accent-color: #1976d2;
    --primary-color: #1976d2;
    --primary-color-alpha: rgba(25, 118, 210, 0.1);
    --hover-bg: var(--accent-hover);

    /* 四个区域的微调颜色  */
    --area-preview: #f9f9f9;
    --area-controls: #f9f9f9;
    --area-result: #f9f9f9;
    --area-buttons: #f9f9f9;

    /* Toast通知变量 - 固定颜色，不随主题变化 */
    --toast-bg: #ffffff;
    --toast-border: #e5e7eb;
    --toast-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --toast-success-bg: rgba(34, 197, 94, 0.1);
    --toast-success-color: #16a34a;
    --toast-error-bg: rgba(239, 68, 68, 0.1);
    --toast-error-color: #dc2626;
    --toast-warning-bg: rgba(251, 191, 36, 0.1);
    --toast-warning-color: #d97706;
    --toast-info-bg: rgba(59, 130, 246, 0.1);
    --toast-info-color: #2563eb;

    /* 字体变量定义 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
    --font-serif: 'Times New Roman', serif;
    --font-emoji: 'Twemoji Country Flags', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;

    /* 字体大小变量 - 增大输入输出框字号 */
    --font-size-xxs: 8px;    /* 超小字体，用于图标文字 */
    --font-size-xs: 10px;    /* 极小字体，用于时间戳等 */
    --font-size-sm: 11px;    /* 小字体，用于按钮文字 */
    --font-size-normal: 12px; /* 正常小字体，用于标签 */
    --font-size-base: 14px;   /* 基础字体，用于选项 - 从13px增大到14px */
    --font-size-md: 15px;     /* 中等字体，用于正文 - 从14px增大到15px */
    --font-size-lg: 16px;     /* 大字体，用于标题 */
    --font-size-xl: 18px;     /* 超大字体，用于主标题 */
    --font-size-xxl: 20px;    /* 特大字体 */
    --font-size-huge: 24px;   /* 巨大字体，用于h1 */

    /* 行高变量 */
    --line-height-tight: 1.2;   /* 紧凑行高，用于标题 */
    --line-height-normal: 1.4;  /* 正常行高 */
    --line-height-relaxed: 1.6; /* 宽松行高，用于正文 */

    /* 字重变量 */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;

    /* 过渡动画变量 */
    --transition-fast: all 0.2s ease;
    --transition-medium: all 0.3s ease;
    --border-radius-sm: 8px;
}

/* 暗色主题（与uTools暗色标签栏颜色完全一致） */
[data-theme="dark"] {
    --bg-primary: #303133;
    --bg-secondary: #303133;
    --bg-tertiary: #3a3c3f;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --border-primary: #4a4c4f;
    --border-secondary: #5a5c5f;
    --accent-primary: #303133;
    --accent-secondary: #ffffff;
    --accent-hover: #3a3c3f;
    --primary-btn-bg: #4a4c4f;
    --primary-btn-text: #ffffff;
    --primary-btn-hover: #5a5c5f;
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);
    --shadow-heavy: rgba(0, 0, 0, 0.5);
    --success-bg: rgba(34, 197, 94, 0.15);
    --success-text: #22c55e;
    --warning-bg: rgba(251, 191, 36, 0.15);
    --warning-text: #fbbf24;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --info-bg: rgba(59, 130, 246, 0.15);
    --info-text: #60a5fa;
    --error-bg: rgba(239, 68, 68, 0.15);
    --error-text: #ef4444;

    /* 强调色 - 用于选中状态等 */
    --accent-color: #42a5f5;
    --primary-color: #42a5f5;
    --primary-color-alpha: rgba(66, 165, 245, 0.15);
    --hover-bg: var(--accent-hover);

    /* 四个区域的微调颜色  */
    --area-preview: #282a2d;
    --area-controls: #282a2d;
    --area-result: #282a2d;
    --area-buttons: #282a2d;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-md); /* 从base提升到md，增大整体基础字号 */
    line-height: var(--line-height-relaxed);
    background: var(--bg-secondary);
    color: var(--text-primary);
    overflow: hidden;
    margin: 0;
    padding: 0;
    /* 改善字体渲染质量 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.view {
    height: 100vh;
    overflow: hidden;
}

/* 主容器布局 */
.main-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--bg-secondary);
}



/* 内容容器 */
.content-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 左侧面板 */
.left-panel {
    width: 280px;
    background: var(--accent-primary);
    padding: 10px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}



.app-title {
    color: var(--accent-secondary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: 0;
    line-height: var(--line-height-tight);
}

.header-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 通用小按钮样式 */
.header-btn, .back-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.header-btn:hover, .back-btn:hover {
    background: var(--bg-secondary);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

/* 图片预览区域 */
.image-preview-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    margin-top: 0px;
    background: var(--area-preview);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
}

.image-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-light);
    transform-origin: center center;
    transition: transform 0.2s ease-out;
    user-select: none;
}

/* 图像缩放容器 */
.zoom-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    cursor: default;
    position: relative;
}

.zoom-container:hover {
    cursor: grab;
}

.zoom-container.dragging {
    cursor: grabbing;
}

/* 缩放比例指示器 - 居中显示，无毛玻璃效果 */
.zoom-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(244, 244, 244, 0.85);
    color: var(--text-primary);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
    user-select: none;
    border: 1px solid rgba(224, 224, 224, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 暗色主题下的缩放指示器 */
[data-theme="dark"] .zoom-indicator {
    background: rgba(40, 40, 40, 0.9);
    color: var(--text-primary);
    border: 1px solid rgba(80, 80, 80, 0.4);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.preview-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px 20px;
    color: var(--text-tertiary);
}

.placeholder-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 12px;
    opacity: 0.6;
    color: var(--text-tertiary);
}

.placeholder-icon svg {
    width: 100%;
    height: 100%;
}

.placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.placeholder-hint {
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.4;
}

/* 底部控制区域 */
.bottom-controls {
    background: var(--area-controls);
    border-radius: 12px;
    padding: 12px;
    border: 1px solid var(--border-primary);
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.action-btn {
    flex: 1;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 12px 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-align: center;
    box-shadow: 0 2px 8px var(--shadow-light);
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
}

.action-btn:hover {
    box-shadow: 0 4px 12px var(--shadow-medium);
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

.action-btn .btn-icon {
    width: 18px;
    height: 18px;
    color: currentColor;
}

.action-btn .btn-icon svg {
    width: 100%;
    height: 100%;
    stroke: currentColor;
}

.action-btn .btn-text {
    font-size: 11px;
    font-weight: 500;
}

/* 换行符切换按钮样式 */
#linebreak-toggle-btn {
    position: relative;
}

/* 换行符图标状态控制 */
#linebreak-toggle-btn .linebreak-icon-enabled,
#linebreak-toggle-btn .linebreak-icon-disabled {
    transition: opacity 0.2s ease;
}

#linebreak-toggle-btn[data-enabled="true"] .linebreak-icon-enabled {
    display: block;
    opacity: 1;
}

#linebreak-toggle-btn[data-enabled="true"] .linebreak-icon-disabled {
    display: none;
    opacity: 0;
}

#linebreak-toggle-btn[data-enabled="false"] .linebreak-icon-enabled {
    display: none;
    opacity: 0;
}

#linebreak-toggle-btn[data-enabled="false"] .linebreak-icon-disabled {
    display: block;
    opacity: 1;
}

/* 状态信息 */
.status-info {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.status-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.status-label {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    margin-bottom: 2px;
}

.status-value {
    color: var(--text-secondary);
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    background: var(--bg-tertiary);
    min-height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-secondary);
}

.status-value.ready {
    background: var(--success-bg);
    color: var(--success-text);
    border-color: var(--success-text);
}

.status-value.processing {
    background: var(--warning-bg);
    color: var(--warning-text);
    border-color: var(--warning-text);
}

.status-value.capturing {
    background: var(--info-bg);
    color: var(--info-text);
    border-color: var(--info-text);
}

.status-value.error {
    background: var(--error-bg);
    color: var(--error-text);
    border-color: var(--error-text);
}

.status-value.unconfigured {
    background: var(--bg-secondary);
    color: var(--text-tertiary);
    border-color: var(--text-tertiary);
}

.status-value.success {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-value.warning {
    color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* 服务切换容器 */
.service-switch-container {
    position: relative;
    display: inline-block;
    z-index: 1;
}

/* 服务切换按钮样式 - 完全遵循status-value.ready的样式逻辑 */
.service-switch-btn {
    /* 继承status-value的完整基础样式 */
    color: #1976d2 !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    /* 使用与状态按钮相同的透明度：亮色模式0.1，暗色模式0.15 */
    background: rgba(25, 118, 210, 0.1) !important;
    /* 固定高度，只允许宽度变化 */
    height: 22px !important;
    min-width: 60px !important;
    display: flex !important;
    align-items: center !important;
    /* 修复：使用space-between来正确分布服务名称和图标 */
    justify-content: space-between !important;
    /* 使用深蓝色边框与浅蓝色背景协调 */
    border: 1px solid #1976d2 !important;

    /* 添加按钮交互样式 */
    cursor: pointer;
    transition: all 0.2s ease;
    box-sizing: border-box !important;
}

.service-switch-btn:hover {
    /* 悬停时稍微加深背景色，使用更高的透明度 */
    background: rgba(25, 118, 210, 0.2) !important;
    /* 保持深蓝色文字和边框颜色 */
    color: #1976d2;
    border-color: #1976d2;
    box-shadow: 0 2px 8px var(--shadow-light);
}

.service-switch-btn .service-name {
    /* 继承父元素的颜色，确保与ready状态按钮一致 */
    color: inherit;
    font-size: 11px !important;
    font-weight: 600 !important;
    /* 移除line-height设置，使用默认值与status-value保持一致 */
    display: flex;
    align-items: center;
}

.service-switch-btn .switch-icon {
    /* 继承父元素的颜色，确保与ready状态按钮一致 */
    color: inherit;
    font-size: 8px;
    opacity: 0.7;
    transition: all 0.2s ease;
    /* 确保图标垂直居中 */
    display: flex;
    align-items: center;
    /* 防止图标收缩 */
    flex-shrink: 0;
}

.service-switch-btn:hover .switch-icon {
    opacity: 0.9;
    transform: rotate(180deg);
}

.service-switch-btn.menu-open .switch-icon {
    transform: rotate(180deg);
    opacity: 0.9;
}

/* 服务切换菜单 */
.service-switch-menu {
    position: fixed;
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 8px 24px var(--shadow-heavy);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 300px;
    overflow-y: auto;
    min-width: 200px;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.service-switch-menu::-webkit-scrollbar {
    display: none;
}

.service-switch-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.service-menu-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: none;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: normal;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-primary);
    position: relative;
    gap: 8px;
}

.service-menu-item:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.service-menu-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.service-menu-item:hover {
    background: var(--bg-secondary);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

/* 选中项的悬停效果 */
.service-menu-item.current:hover {
    background: rgba(25, 118, 210, 0.12);
    transform: translateX(2px);
}

.service-menu-item.current {
    /* 使用淡蓝色背景高亮 */
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    font-weight: 500;
}

.service-menu-item.current::after {
    content: '';
}

.service-menu-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.service-menu-item.disabled:hover {
    background: none;
    color: var(--text-primary);
}

/* 服务菜单图标样式 */
.service-menu-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-menu-icon svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

/* 服务菜单文本样式 */
.service-menu-text {
    flex: 1;
    font-size: 13px;
    font-weight: normal;
    color: inherit;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
}

/* 暗色主题下的服务切换按钮和菜单适配 */
[data-theme="dark"] .service-switch-btn {
    /* 暗色模式下使用与状态按钮相同的透明度0.15 */
    background: rgba(25, 118, 210, 0.15) !important;
}

[data-theme="dark"] .service-switch-btn:hover {
    /* 暗色模式下悬停时使用更高的透明度 */
    background: rgba(25, 118, 210, 0.25) !important;
}

[data-theme="dark"] .service-switch-menu {
    background: var(--bg-primary);
    border-color: var(--border-primary);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6);
}

/* 暗色主题下的选中状态 */
[data-theme="dark"] .service-menu-item.current {
    background: rgba(25, 118, 210, 0.15);
    color: #64b5f6;
}

[data-theme="dark"] .model-option.selected {
    background: rgba(25, 118, 210, 0.15);
    color: #64b5f6;
}

/* 暗色主题下选中项的悬停效果 */
[data-theme="dark"] .service-menu-item.current:hover {
    background: rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .model-option.selected:hover {
    background: rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .service-menu-item {
    color: var(--text-primary);
    border-bottom-color: var(--border-primary);
}

[data-theme="dark"] .service-menu-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* 暗色主题下的服务列表样式适配 */



[data-theme="dark"] .service-item {
    background: var(--bg-secondary);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .service-item:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .service-item.active {
    background: var(--primary-btn-bg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border-color: var(--border-secondary);
}

/* 暗色主题下的基础配置页面样式适配 */
[data-theme="dark"] .config-item {
    background: var(--bg-secondary);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .config-item:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .config-item.active {
    background: var(--primary-btn-bg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .config-item-row {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
}

[data-theme="dark"] .config-item-row:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 暗色主题下的快捷键配置样式 */
[data-theme="dark"] .shortcut-item:hover {
    background: var(--bg-tertiary);
}

/* 暗色主题下的可选配置项样式 */
[data-theme="dark"] .config-option-item:hover {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .config-option-item.config-sub-item {
    background: var(--bg-secondary);
}

[data-theme="dark"] .config-option-item.config-sub-item:hover {
    background: var(--bg-tertiary);
}

[data-theme="dark"] .config-help-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-theme="dark"] .custom-tooltip {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .custom-tooltip::before {
    border-top-color: var(--bg-secondary);
}

[data-theme="dark"] .custom-tooltip::after {
    border-top-color: var(--border-primary);
}

/* 工具提示在下方显示时的箭头样式 - 箭头指向上方 */
.custom-tooltip.tooltip-bottom::after {
    bottom: auto;
    top: -6px;
    border-top: none;
    border-bottom: 6px solid var(--border-primary);
    z-index: 1;
}

.custom-tooltip.tooltip-bottom::before {
    bottom: auto;
    top: -5px;
    border-top: none;
    border-bottom: 5px solid var(--bg-secondary);
    z-index: 2;
}

[data-theme="dark"] .custom-tooltip.tooltip-bottom::before {
    border-bottom-color: var(--bg-secondary);
}

[data-theme="dark"] .custom-tooltip.tooltip-bottom::after {
    border-bottom-color: var(--border-primary);
}

[data-theme="dark"] .shortcut-input {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .shortcut-input:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .shortcut-input:focus {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

[data-theme="dark"] .shortcut-input.recording {
    background: rgba(251, 191, 36, 0.1);
    border-color: var(--warning-text);
}

[data-theme="dark"] .shortcut-input.conflict {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--error-text);
}



/* 暗色主题下的状态指示器适配 */
[data-theme="dark"] .service-status-indicator[data-status="ready"] {
    background: var(--success-text);
    box-shadow: 0 0 4px rgba(34, 197, 94, 0.4);
}

[data-theme="dark"] .service-status-indicator[data-status="error"] {
    background: var(--error-text);
    box-shadow: 0 0 4px rgba(239, 68, 68, 0.4);
}

[data-theme="dark"] .service-status-indicator[data-status="unknown"] {
    background: var(--text-tertiary);
    box-shadow: 0 0 4px rgba(153, 153, 153, 0.3);
}

[data-theme="dark"] .service-status-indicator[data-status="unconfigured"] {
    background: var(--text-tertiary);
    box-shadow: 0 0 4px rgba(153, 153, 153, 0.3);
}



/* 服务项焦点状态 */
.service-item:focus {
    outline: 2px solid var(--accent-secondary);
    outline-offset: 2px;
}

/* 禁用状态（如果需要） */
.service-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 加载面板 */
.loading-panel {
    position: absolute;
    top: 31%;
    left: 50%;
    width: calc(100% - 75px);
    height: auto;
    min-height: 50px;
    background: var(--accent-primary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 20px;
    margin-top: -25px;
    margin-left: calc(-50% + 37.5px);
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;
}

.loading-title {
    color: var(--accent-secondary);
    font-size: 14px;
    font-weight: 600;
}

.loading-desc {
    color: var(--text-tertiary);
    font-size: 12px;
}

/* 右侧面板 */
.right-panel {
    flex: 1;
    background: var(--bg-primary);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 10px;
}



.result-title {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: 0;
    line-height: var(--line-height-tight);
}

.result-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 12px;
    margin-top: 0px;
    position: relative; /* 为绝对定位的复制按钮提供定位上下文 */
    min-height: 0; /* 确保flex子元素可以正确收缩 */
}

/* 历史记录独立复制按钮样式 */
.history-copy-btn {
    position: absolute;
    bottom: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.history-copy-btn:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

.history-copy-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.history-copy-btn .btn-icon {
    width: 16px;
    height: 16px;
}

/* 历史记录复制按钮主题适配 */
[data-theme="dark"] .history-copy-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .history-copy-btn:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
}

[data-theme="light"] .history-copy-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

[data-theme="light"] .history-copy-btn:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

[data-theme="auto"] .history-copy-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

[data-theme="auto"] .history-copy-btn:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
}

/* 翻译记录分栏复制按钮特定样式 */
.translate-source-copy-btn,
.translate-target-copy-btn {
    position: absolute;
    bottom: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.translate-source-copy-btn:hover,
.translate-target-copy-btn:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

.translate-source-copy-btn:active,
.translate-target-copy-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.translate-source-copy-btn .btn-icon,
.translate-target-copy-btn .btn-icon {
    width: 16px;
    height: 16px;
}

/* 翻译记录复制按钮主题适配 */
[data-theme="dark"] .translate-source-copy-btn,
[data-theme="dark"] .translate-target-copy-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .translate-source-copy-btn:hover,
[data-theme="dark"] .translate-target-copy-btn:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
}

[data-theme="light"] .translate-source-copy-btn,
[data-theme="light"] .translate-target-copy-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

[data-theme="light"] .translate-source-copy-btn:hover,
[data-theme="light"] .translate-target-copy-btn:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

[data-theme="auto"] .translate-source-copy-btn,
[data-theme="auto"] .translate-target-copy-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

[data-theme="auto"] .translate-source-copy-btn:hover,
[data-theme="auto"] .translate-target-copy-btn:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
}

/* 单栏结果容器 */
.single-result-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 双栏结果容器 */
.dual-result-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    gap: 16px;
}

/* 双栏操作按钮区域 */
.dual-action-buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    margin: -8px 0;
    padding: 0;
    min-height: 0;
}

/* 空白状态样式 */
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 120px;
    color: var(--text-secondary);
    font-size: 14px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px dashed var(--border-color);
}

/* 渲染结果区域 */
.rendered-result-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    background: var(--area-result);
    overflow: hidden;
}

/* 原始结果区域 */
.raw-result-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    background: var(--area-result);
    overflow: hidden;
}

/* 渲染结果内容区域 */
.rendered-result-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
    background: var(--area-result);
    border-radius: 12px;
    font-family: var(--font-family);
    color: var(--text-primary);
    line-height: 1.6;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.rendered-result-content::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 原始结果文本框 - 优化字体配置 */
.raw-result-text {
    flex: 1;
    background: transparent;
    border: none;
    border-radius: 12px;
    padding: 16px;
    font-family: var(--font-mono); /* 使用等宽字体，便于查看原始数据 */
    font-size: var(--font-size-md); /* 从base提升到md，增大字号 */
    line-height: var(--line-height-relaxed); /* 使用宽松行高，提升可读性 */
    color: var(--text-primary);
    resize: none;
    outline: none;
    white-space: pre-wrap; /* 保持原始格式 */
    word-wrap: break-word; /* 长单词换行 */
    letter-spacing: 0.02em; /* 轻微增加字间距，提升等宽字体可读性 */

    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.raw-result-text::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.raw-result-text:focus {
    outline: none;
    background: transparent;
}



/* Markdown渲染样式 */
.rendered-result-content h1,
.rendered-result-content h2,
.rendered-result-content h3,
.rendered-result-content h4,
.rendered-result-content h5,
.rendered-result-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: var(--text-primary);
}

.rendered-result-content h1 { font-size: 24px; }
.rendered-result-content h2 { font-size: 20px; }
.rendered-result-content h3 { font-size: 18px; }
.rendered-result-content h4 { font-size: 16px; }
.rendered-result-content h5 { font-size: 14px; }
.rendered-result-content h6 { font-size: 12px; }

.rendered-result-content p {
    margin: 8px 0;
}

.rendered-result-content ul,
.rendered-result-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.rendered-result-content li {
    margin: 4px 0;
}

.rendered-result-content blockquote {
    margin: 16px 0;
    padding: 8px 16px;
    border-left: 4px solid var(--border-secondary);
    background: var(--bg-secondary);
    border-radius: 0 8px 8px 0;
}

.rendered-result-content code {
    background: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: var(--font-mono);
    font-size: 13px;
}

.rendered-result-content pre {
    background: var(--bg-secondary);
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
}

.rendered-result-content pre code {
    background: none;
    padding: 0;
}

/* 数学公式样式 */
.rendered-result-content .math-block {
    margin: 16px 0;
    text-align: center;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 8px;
    overflow-x: auto;
}

.rendered-result-content .math-inline {
    padding: 2px 4px;
    background: var(--bg-secondary);
    border-radius: 4px;
    font-family: var(--font-serif);
}

/* 图片样式 */
.rendered-result-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 8px 0;
}

/* 删除线样式 */
.rendered-result-content del {
    text-decoration: line-through;
    color: var(--text-secondary);
}

/* 表格渲染样式 */
.rendered-result-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
    background: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
}

.rendered-result-content th,
.rendered-result-content td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-primary);
    border-right: 1px solid var(--border-primary);
}

.rendered-result-content th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

.rendered-result-content td {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.rendered-result-content th:last-child,
.rendered-result-content td:last-child {
    border-right: none;
}

.rendered-result-content tr:last-child td {
    border-bottom: none;
}

/* 公式渲染样式 */
.rendered-result-content .katex {
    font-size: 1.1em;
}

.rendered-result-content .katex-display {
    margin: 16px 0;
    text-align: center;
}

/* 错误提示样式 */
.render-error {
    color: var(--text-error);
    background: var(--bg-error);
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--border-error);
    margin: 8px 0;
    font-size: 14px;
}

.render-error-title {
    font-weight: 600;
    margin-bottom: 4px;
}

/* 空状态样式 */
.render-empty {
    color: var(--text-tertiary);
    text-align: center;
    padding: 32px;
    font-style: italic;
}

/* 公式渲染样式增强 */
.formula-inline {
    display: inline-block;
    margin: 0 2px;
    vertical-align: middle;
}

.formula-display {
    margin: 12px 0;
    text-align: center;
    overflow-x: auto;
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
}

.formula-block-simple {
    background: var(--bg-secondary);
    padding: 12px 16px;
    border-radius: 8px;
    font-family: var(--font-mono);
    font-size: 16px;
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    margin: 12px 0;
    text-align: center;
    overflow-x: auto;
}

.formula-text {
    margin: 8px 0;
    padding: 4px 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    font-family: var(--font-mono);
    color: var(--text-secondary);
}

.formula-fallback {
    margin-top: 8px;
    padding: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    font-family: var(--font-mono);
    font-size: 14px;
    color: var(--text-secondary);
    border: 1px dashed var(--border-secondary);
}

.formula-loading {
    color: var(--text-tertiary);
    font-style: italic;
    margin-bottom: 8px;
    text-align: center;
}

/* 公式渲染错误样式 */
.formula-error {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    color: #c53030;
    padding: 12px;
    border-radius: 6px;
    margin: 8px 0;
}

.formula-warning {
    background: #fffaf0;
    border: 1px solid #fbd38d;
    color: #c05621;
    padding: 12px;
    border-radius: 6px;
    margin: 8px 0;
}

/* 暗色主题下的错误样式 */
[data-theme="dark"] .formula-error {
    background: #2d1b1b;
    border-color: #4a2c2c;
    color: #fc8181;
}

[data-theme="dark"] .formula-warning {
    background: #2d2416;
    border-color: #4a3d1a;
    color: #f6ad55;
}

/* KaTeX样式覆盖 */
.katex {
    font-size: 1.1em !important;
}

.katex-display {
    margin: 0.5em 0 !important;
}

/* 确保KaTeX在暗色主题下正常显示 */
[data-theme="dark"] .katex {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .katex .base {
    color: var(--text-primary) !important;
}



/* 底部控制区域 */
.result-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--area-buttons);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    gap: 3px;
}

.left-controls, .center-controls, .right-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 识别模式选择器 */
.recognition-mode-container {
    position: relative;
    display: inline-block;
    flex: none;
    width: 100px;
}

.recognition-mode-menu {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: var(--area-buttons);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 -8px 24px var(--shadow-medium);
    z-index: 1000;
    max-height: 250px;
    overflow-y: auto;
    display: none;
    margin-bottom: 4px;
    scrollbar-width: thin;
    scrollbar-color: var(--text-tertiary) transparent;
}

/* 识别模式菜单滚动条样式 */
.recognition-mode-menu::-webkit-scrollbar {
    width: 6px;
}

.recognition-mode-menu::-webkit-scrollbar-track {
    background: transparent;
}

.recognition-mode-menu::-webkit-scrollbar-thumb {
    background-color: var(--text-tertiary);
    border-radius: 3px;
    border: none;
}

.recognition-mode-menu::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

.recognition-mode-menu.show {
    display: block;
}

.mode-option {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mode-option .mode-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: currentColor;
}

.mode-option:last-child {
    border-bottom: none;
}

.mode-option:hover {
    background: var(--bg-secondary);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

/* 当前选中状态 - 蓝色高亮背景 */
.mode-option.active {
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    font-weight: 500;
}

/* 当前选中项的悬停效果 */
.mode-option.active:hover {
    background: rgba(25, 118, 210, 0.12);
    transform: translateX(2px);
}

/* 备用选中状态类（与模型选择菜单保持一致） */
.mode-option.selected {
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    font-weight: 500;
}

/* 备用选中项的悬停效果 */
.mode-option.selected:hover {
    background: rgba(25, 118, 210, 0.12);
    transform: translateX(2px);
}

/* 暗色主题下的识别模式选择器适配 */
[data-theme="dark"] .mode-option.active {
    background: rgba(25, 118, 210, 0.15);
    color: #64b5f6;
}

[data-theme="dark"] .mode-option.active:hover {
    background: rgba(25, 118, 210, 0.2);
}

[data-theme="dark"] .mode-option.selected {
    background: rgba(25, 118, 210, 0.15);
    color: #64b5f6;
}

[data-theme="dark"] .mode-option.selected:hover {
    background: rgba(25, 118, 210, 0.2);
}

#recognition-mode-btn {
    width: 100px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    font-family: inherit;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    transition: var(--transition-fast);
    min-height: 32px;
    box-sizing: border-box;
}

#recognition-mode-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

#recognition-mode-btn .mode-text {
    flex: 1;
    text-align: left;
    font-size: 11px;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#recognition-mode-btn .mode-arrow {
    font-size: 8px;
    color: var(--text-secondary);
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

#recognition-mode-btn.active .mode-arrow {
    transform: rotate(180deg);
}

.center-controls {
    flex: 1;
    justify-content: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
    cursor: pointer;
    padding: 4px 8px;
    line-height: 1.2;
    white-space: nowrap;
}

.checkbox-label:hover {
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    margin-right: 6px;
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

/* 按钮样式的复选框 */
.checkbox-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6.5px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
}

.checkbox-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.checkbox-btn input[type="checkbox"] {
    margin: 0;
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}

.checkbox-text {
    font-weight: 500;
    font-size: 11px;
}

/* 说明按钮样式 */
.help-btn {
    margin-left: 8px;
    padding: 4px 8px;
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    top: -4px;  /* 单独调整按钮垂直位置，不影响标题文字 */
}

.help-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 4px var(--shadow-light);
}



/* Toast通知样式 */
.toast {
    position: fixed;
    top: 1.6rem;
    right: 1rem;
    background: var(--toast-bg);
    color: #374151;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 9999;
    border: 1.5px solid var(--toast-border);
    box-shadow: var(--toast-shadow);
    transition: all 0.3s ease;
    opacity: 0;
    pointer-events: none;
    display: block;
    text-align: left;
    letter-spacing: 0.025em;
    max-width: min(400px, calc(100vw - 2rem));
    min-width: 120px;
    width: auto;
    box-sizing: border-box;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.4;
    transform: translateX(100%) translateY(0px);
}

.toast.show {
    opacity: 1;
    pointer-events: auto;
    transform: translateX(0) translateY(var(--toast-offset, 0px));
}

.toast.success {
    background: var(--toast-success-bg);
    color: var(--toast-success-color);
    border-color: var(--toast-success-border);
}

.toast.error {
    background: var(--toast-error-bg);
    color: var(--toast-error-color);
    border-color: var(--toast-error-border);
}

.toast.info {
    background: var(--toast-info-bg);
    color: var(--toast-info-color);
    border-color: var(--toast-info-border);
}

.toast.warning {
    background: var(--toast-warning-bg);
    color: var(--toast-warning-color);
    border-color: var(--toast-warning-border);
}

.toast.hide {
    opacity: 0;
    transform: translateX(100%) translateY(var(--toast-offset, 0px));
}

/* Toast容器 - 用于管理多个Toast */
.toast-container {
    position: fixed;
    top: 1.6rem;
    right: 1rem;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    pointer-events: none;
    max-width: min(350px, calc(100vw - 2rem));
    width: auto;
    box-sizing: border-box;
}

.toast-container .toast {
    position: fixed;
    top: 1.6rem;
    right: 1rem;
    pointer-events: auto;
}

/* 自定义模型标签特殊样式 */
label:has(#use-custom-model) {
    /* 让自定义模型的标签更加低调 */
    font-size: 12px;
    color: var(--text-tertiary);
    padding: 6px 0;
    /* 确保复选框和文字垂直对齐 */
    display: flex;
    align-items: center;
    line-height: 1.4;
}

label:has(#use-custom-model):hover {
    color: var(--text-secondary);
}

.control-buttons {
    display: flex;
    gap: 8px;
}

/* 控制按钮统一样式 */
.control-btn {
    padding: 8px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    min-width: 32px;
    min-height: 32px;
    box-sizing: border-box;
}

.control-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

/* 只有图标的按钮（设置、主题切换）保持正方形 */
.control-btn:not(:has(.btn-text)) {
    width: 32px;
    height: 32px;
    padding: 8px;
}

/* 有文字的按钮（复制、清空）使用水平padding */
.control-btn:has(.btn-text) {
    padding: 8px 12px;
    min-width: auto;
    min-height: 32px;
}

.control-btn .btn-icon {
    width: 14px;
    height: 14px;
    color: currentColor;
}

.control-btn .btn-icon svg {
    width: 100%;
    height: 100%;
    stroke: currentColor;
}

.control-btn .btn-text {
    font-size: 11px;
}

#re-recognize-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

#re-recognize-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.result-text {
    flex: 1;
    background: var(--area-result);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 16px;
    font-family: var(--font-family);
    font-size: var(--font-size-lg); /* 从base提升到lg，增大字号 */
    line-height: var(--line-height-relaxed);
    white-space: pre-wrap;
    word-wrap: break-word;
    resize: none;
    width: 100%;
    box-sizing: border-box;
    color: var(--text-primary);
    overflow-y: auto;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.result-text::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.result-text:focus {
    outline: none;
    border-color: var(--border-secondary);
    background: var(--area-result);
}

/* 公式、表格、Markdown结果容器样式 */
.formula-result-container,
.table-result-container,
.markdown-result-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 12px;
}

.rendered-result,
.raw-result {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}



/* 渲染结果区域样式 - 优化字体配置 */
.formula-rendered,
.table-rendered,
.markdown-rendered {
    flex: 1;
    background: var(--area-result);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 16px;
    overflow-y: auto;
    min-height: 0;
    font-family: var(--font-family); /* 统一字体族 */
    font-size: var(--font-size-lg); /* 从md提升到lg，增大字号 */
    line-height: var(--line-height-relaxed); /* 宽松行高 */
    color: var(--text-primary);
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.formula-rendered::-webkit-scrollbar,
.table-rendered::-webkit-scrollbar,
.markdown-rendered::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 原始结果文本框样式 */
.raw-text {
    min-height: 120px;
    font-family: var(--font-mono);
    font-size: var(--font-size-sm);
}

/* 公式渲染样式 */
.formula-rendered {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

.formula-rendered .formula-item {
    margin: 8px 0;
    padding: 8px;
    border-radius: 8px;
    background: var(--bg-primary);
}

/* 表格渲染样式 - 优化字体配置 */
.table-rendered table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-family: var(--font-family);
    font-size: var(--font-size-md); /* 从base提升到md，增大表格字号 */
    line-height: var(--line-height-normal); /* 表格使用正常行高，避免过于松散 */
}

.table-rendered th,
.table-rendered td {
    border: 1px solid var(--border-primary);
    padding: 8px 12px;
    text-align: left;
    vertical-align: top;
}

.table-rendered th {
    background: var(--bg-secondary);
    font-weight: var(--font-weight-semibold); /* 使用统一的字重变量 */
    color: var(--text-primary);
    font-size: var(--font-size-md); /* 从base提升到md，确保表头字体大小一致 */
}

.table-rendered td {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-md); /* 从base提升到md，确保表格内容字体大小一致 */
    font-weight: var(--font-weight-normal); /* 使用正常字重 */
}

/* Markdown渲染样式 */
.markdown-rendered {
    line-height: 1.6;
    color: var(--text-primary);
}

.markdown-rendered h1,
.markdown-rendered h2,
.markdown-rendered h3,
.markdown-rendered h4,
.markdown-rendered h5,
.markdown-rendered h6 {
    margin: 16px 0 8px 0;
    color: var(--text-primary);
    font-weight: 600;
}

.markdown-rendered h1 { font-size: var(--font-size-huge); font-weight: var(--font-weight-semibold); }
.markdown-rendered h2 { font-size: var(--font-size-xxl); font-weight: var(--font-weight-semibold); }
.markdown-rendered h3 { font-size: var(--font-size-xl); font-weight: var(--font-weight-semibold); }
.markdown-rendered h4 { font-size: var(--font-size-lg); font-weight: var(--font-weight-medium); }
.markdown-rendered h5 { font-size: var(--font-size-md); font-weight: var(--font-weight-medium); }
.markdown-rendered h6 { font-size: var(--font-size-normal); font-weight: var(--font-weight-medium); }

.markdown-rendered p {
    margin: 8px 0;
}

.markdown-rendered ul,
.markdown-rendered ol {
    margin: 8px 0;
    padding-left: 24px;
}

.markdown-rendered li {
    margin: 4px 0;
}

.markdown-rendered blockquote {
    margin: 16px 0;
    padding: 8px 16px;
    border-left: 4px solid var(--border-secondary);
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.markdown-rendered code {
    background: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: var(--font-mono);
    font-size: var(--font-size-md); /* 从base提升到md，增大行内代码字号 */
    font-weight: var(--font-weight-normal);
    letter-spacing: 0.02em; /* 等宽字体增加字间距提升可读性 */
    color: var(--text-primary);
}

.markdown-rendered pre {
    background: var(--bg-secondary);
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid var(--border-primary);
    font-family: var(--font-mono);
    font-size: var(--font-size-md); /* 从base提升到md，增大代码块字号 */
    line-height: var(--line-height-normal); /* 代码块使用正常行高 */
    font-weight: var(--font-weight-normal);
    letter-spacing: 0.02em; /* 等宽字体增加字间距 */
}

.markdown-rendered pre code {
    background: none;
    padding: 0;
    font-family: inherit; /* 继承pre的字体设置 */
    font-size: inherit;
    line-height: inherit;
    letter-spacing: inherit;
}

.markdown-rendered table {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
}

.markdown-rendered th,
.markdown-rendered td {
    border: 1px solid var(--border-primary);
    padding: 8px 12px;
    text-align: left;
}

.markdown-rendered th {
    background: var(--bg-secondary);
    font-weight: 600;
}

.markdown-rendered hr {
    margin: 16px 0;
    border: none;
    border-top: 1px solid var(--border-secondary);
    height: 1px;
}

/* 数学公式样式 */
.markdown-rendered .math-block {
    margin: 16px 0;
    text-align: center;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 8px;
    overflow-x: auto;
}

.markdown-rendered .math-inline {
    padding: 2px 4px;
    background: var(--bg-secondary);
    border-radius: 4px;
    font-family: 'Times New Roman', serif;
}

/* 图片样式 */
.markdown-rendered img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 8px 0;
}

/* 删除线样式 */
.markdown-rendered del {
    text-decoration: line-through;
    color: var(--text-secondary);
}

/* 渲染结果状态样式 */
.empty-result,
.render-error,
.render-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    text-align: center;
    color: var(--text-secondary);
    font-size: 14px;
    border-radius: 8px;
    padding: 20px;
}

.render-error {
    background: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
    border: 1px solid rgba(255, 59, 48, 0.2);
}

.render-fallback {
    background: rgba(255, 149, 0, 0.1);
    color: #ff9500;
    border: 1px solid rgba(255, 149, 0, 0.2);
}

.render-fallback pre {
    background: none;
    margin: 8px 0 0 0;
    padding: 0;
    font-size: 12px;
    text-align: left;
    max-height: 200px;
    overflow-y: auto;
}

/* 简单表格样式（备用方案） - 优化字体配置 */
.simple-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-family: var(--font-family);
    font-size: var(--font-size-md); /* 从base提升到md，增大简单表格字号 */
    line-height: var(--line-height-normal); /* 表格使用正常行高 */
    font-weight: var(--font-weight-normal);
}

.simple-table th,
.simple-table td {
    border: 1px solid var(--border-primary);
    padding: 8px 12px;
    text-align: left;
    vertical-align: top;
}

.simple-table th {
    background: var(--bg-secondary);
    font-weight: var(--font-weight-semibold); /* 使用统一的字重变量 */
    color: var(--text-primary);
    font-size: var(--font-size-md); /* 从base提升到md，确保表头字体大小一致 */
}

.simple-table td {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-md); /* 从base提升到md，确保表格内容字体大小一致 */
    font-weight: var(--font-weight-normal); /* 使用正常字重 */
}

/* 渲染结果状态样式 - 优化字体配置 */
.empty-result,
.render-error,
.render-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    text-align: center;
    color: var(--text-secondary);
    font-family: var(--font-family);
    font-size: var(--font-size-base); /* 使用统一的基础字体大小 */
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed); /* 宽松行高，便于阅读状态信息 */
    border-radius: 8px;
    padding: 20px;
}

.render-error {
    background: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
    border: 1px solid rgba(255, 59, 48, 0.2);
}

.render-fallback {
    background: rgba(255, 149, 0, 0.1);
    color: #ff9500;
    border: 1px solid rgba(255, 149, 0, 0.2);
}

.render-fallback pre {
    background: none;
    margin: 8px 0 0 0;
    padding: 0;
    font-size: 12px;
    text-align: left;
    max-height: 200px;
    overflow-y: auto;
}

/* 简单表格样式（备用方案） */
.simple-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: 14px;
}

.simple-table th,
.simple-table td {
    border: 1px solid var(--border-primary);
    padding: 8px 12px;
    text-align: left;
    vertical-align: top;
}

.simple-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

.simple-table td {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.result-text::placeholder {
    color: var(--text-tertiary);
    font-style: normal;
}

/* 旧的loading样式保持兼容 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--accent-secondary);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-scale {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 配置界面样式 */
#config-view {
    background: var(--bg-secondary);
    height: 100vh;
    overflow: hidden;
}

/* 配置界面左侧服务列表区域 */
.service-list-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--area-preview);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    margin-bottom: 0px;
}

/* 基础配置页面左侧配置列表区域 */
.config-list-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--area-preview);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    margin-bottom: 0px;
}



.service-list {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 8px 0;
}

.service-list::-webkit-scrollbar {
    display: none;
}

/* 基础配置页面配置列表 */
.config-list {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 8px 0;
}

.config-list::-webkit-scrollbar {
    display: none;
}

.service-item {
    padding: 14px 18px;
    cursor: pointer;
    border: none;
    border-radius: 10px;
    margin: 6px 12px;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    background: var(--bg-secondary);
    box-shadow: 0 1px 3px var(--shadow-light);
    gap: 12px;
    position: relative;
}

.service-item:last-child {
    border-bottom: none;
}

.service-item:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

.service-item.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    font-weight: 600;
    box-shadow: 0 2px 8px var(--shadow-medium);
    border: 1px solid var(--border-secondary);
}

.service-name {
    font-size: 14px;
    color: inherit;
    font-weight: 500;
    flex: 1;
}

/* 基础配置页面配置项样式 */
.config-item {
    padding: 14px 18px;
    cursor: pointer;
    border: none;
    border-radius: 10px;
    margin: 6px 12px;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    background: var(--bg-secondary);
    box-shadow: 0 1px 3px var(--shadow-light);
    gap: 12px;
    position: relative;
}

.config-item:last-child {
    border-bottom: none;
}

.config-item:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

.config-item.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    font-weight: 600;
    box-shadow: 0 2px 8px var(--shadow-medium);
    border: 1px solid var(--border-secondary);
}

.config-name {
    font-size: 14px;
    color: inherit;
    font-weight: 500;
    flex: 1;
}

.config-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.config-item:hover .config-icon {
    transform: scale(1.05);
    color: var(--text-primary);
}

.config-item.active .config-icon {
    color: var(--primary-btn-text);
}

/* 服务状态指示器 */
.service-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
    transition: var(--transition-fast);
}

.service-status-indicator[data-status="ready"] {
    background: var(--success-text);
    box-shadow: 0 0 4px rgba(34, 197, 94, 0.3);
}

.service-status-indicator[data-status="error"] {
    background: var(--error-text);
    box-shadow: 0 0 4px rgba(239, 68, 68, 0.3);
}

.service-status-indicator[data-status="unknown"] {
    background: var(--text-tertiary);
    box-shadow: 0 0 4px rgba(153, 153, 153, 0.2);
}

.service-status-indicator[data-status="unconfigured"] {
    background: var(--text-tertiary);
    box-shadow: 0 0 4px rgba(153, 153, 153, 0.2);
}

/* 服务图标 */
.service-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.service-icon svg {
    width: 20px;
    height: 20px;
}

/* OpenAI图标使用当前文字颜色 */
.service-item[data-service="openai"] .service-icon svg {
    color: var(--text-primary);
}

/* 图标悬停效果 */
.service-item:hover .service-icon {
    transform: scale(1.05);
}

/* 配置界面右侧内容区域 */
.config-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--area-result);
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    margin-bottom: 12px;
}

.config-content {
    flex: 1;
    padding: 2px;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.config-content::-webkit-scrollbar-track,
.config-content::-webkit-scrollbar-track-piece {
    background-color: transparent;
}

.config-content::-webkit-scrollbar {
    width: 8px;
}

.config-content::-webkit-scrollbar-thumb {
    background-color: var(--text-tertiary);
    border-radius: 4px;
    border: none;
}

.config-content::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

/* 模型服务页面专用样式 */
#model-service-page .config-content {
    padding: 16px;
}

/* 配置页面容器 */
.config-page {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 0;
}

/* 配置页面占位符样式 */
.config-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-tertiary);
    text-align: center;
}

.config-placeholder .placeholder-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.config-placeholder .placeholder-text {
    font-size: 14px;
    font-weight: 500;
}

.config-placeholder .placeholder-desc {
    font-size: 12px;
    color: var(--text-tertiary);
    margin-top: 8px;
    opacity: 0.8;
}

/* 基础配置页面样式 */
.base-config-section {
    padding: 20px;
}

.config-section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-primary);
}

/* 识别类型配置样式 */
.recognition-type-config {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    box-shadow: 0 1px 3px var(--shadow-light);
}

/* 最后一个区域（markdown）的特殊间距 */
.recognition-type-config:last-child {
    margin-bottom: 0px;
}

.recognition-type-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 12px;
}

/* 包含开关的header使用两端对齐 */
.recognition-type-header.with-switch {
    justify-content: space-between;
}

/* 识别类型图标 */
.recognition-type-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    color: var(--primary-color);
}

.recognition-type-icon svg {
    width: 20px;
    height: 20px;
}

.recognition-type-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.recognition-type-content {
    margin-top: 0;
}

.model-selection-row {
    display: flex;
    align-items: center;
    gap: 0px;
}

.model-selection-row label {
    min-width: 70px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.model-selection-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    justify-content: space-between;
}

.model-select-container {
    position: relative;
    display: inline-block;
    flex: 1;
    min-width: 200px;
    max-width: 300px;
}

.model-select-btn {
    width: 100%;
    background: var(--area-buttons);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-family: inherit;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    transition: all 0.2s ease;
    min-height: 32px;
    box-sizing: border-box;
}

.model-select-btn:hover {
    border-color: var(--border-secondary);
    background-color: var(--bg-secondary);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.model-select-btn:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

/* 模型选择按钮图标 */
.model-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.model-icon svg {
    width: 16px;
    height: 16px;
}

/* 确保OpenAI图标在按钮中使用当前文字颜色 */
.model-select-btn .model-icon svg[title="OpenAI"] {
    color: var(--text-primary);
}

.model-text {
    flex: 1;
    text-align: left;
    font-size: 13px;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.model-arrow {
    font-size: 10px;
    color: var(--text-secondary);
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

.model-select-btn.active .model-arrow {
    transform: rotate(180deg);
}

.model-select-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--area-buttons);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 8px 24px var(--shadow-medium);
    z-index: 1000;
    max-height: 250px;
    overflow-y: auto;
    display: none;
    margin-top: 4px;
    scrollbar-width: thin;
    scrollbar-color: var(--text-tertiary) transparent;
}

/* 模型选择菜单滚动条样式 */
.model-select-menu::-webkit-scrollbar {
    width: 6px;
}

.model-select-menu::-webkit-scrollbar-track {
    background: transparent;
}

.model-select-menu::-webkit-scrollbar-thumb {
    background-color: var(--text-tertiary);
    border-radius: 3px;
    border: none;
}

.model-select-menu::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

.model-select-menu.show {
    display: block;
}

/* 公式识别和markdown区域的选项框向上展开 */
#formula-model-menu,
#markdown-model-menu {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: 4px;
}

.model-option {
    padding: 8px 12px; /* 调节单个选项的内边距，可修改此值来调整选项高度 */
    cursor: pointer;
    font-size: 13px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.model-option:last-child {
    border-bottom: none;
}

.model-option:hover {
    background: var(--bg-secondary);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

.model-option.active {
    background: var(--primary-color);
    color: white;
}

/* 选中状态高亮 - 淡蓝色背景，便于识别 */
.model-option.selected {
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    font-weight: 500;
}

/* 选中项的悬停效果 */
.model-option.selected:hover {
    background: rgba(25, 118, 210, 0.12);
    transform: translateX(2px);
}

.model-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.model-option.disabled:hover {
    background: none;
}

/* 模型选项图标容器 */
.model-option-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.model-option-icon svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

/* 确保OpenAI图标在模型选项中使用当前文字颜色 */
.model-option[data-service="openai"] .model-option-icon svg {
    color: var(--text-primary);
}

/* 模型选项文本容器 */
.model-option-text {
    flex: 1;
    font-size: 13px;
    font-weight: normal;
    color: inherit;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
}

.config-btn {
    width: 32px;
    height: 32px;
    padding: 6px;
    background: var(--primary-btn-bg);
    border: 1px solid var(--border-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-left: auto;
}

.config-btn:hover {
    background: var(--primary-btn-hover);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.config-btn .icon-svg {
    width: 14px;
    height: 14px;
    color: var(--text-primary);
    transition: color 0.2s ease;
}

/* 配置图标主题适配 */
.config-icon .icon-svg {
    width: 16px;
    height: 16px;
    color: var(--text-primary);
    transition: color 0.2s ease;
}

/* 确保SVG图标正确继承颜色 */
.config-icon svg,
.config-btn svg {
    color: inherit;
    stroke: currentColor;
    fill: none;
}

/* 统一选项卡图标大小 */
.config-icon .lucide-database-backup {
    width: 16px;
    height: 16px;
}

/* 对于有fill属性的元素，使用currentColor */
.config-icon svg [fill="currentColor"],
.config-btn svg [fill="currentColor"] {
    fill: currentColor;
}

/* 提示词配置弹窗样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    box-shadow: 0 4px 20px var(--shadow-heavy);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-tertiary);
}

.modal-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--accent-hover);
    color: var(--text-primary);
}

.modal-refresh-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.modal-refresh-btn:hover {
    background: var(--accent-hover);
    color: var(--text-primary);
}

.modal-refresh-btn svg {
    width: 16px;
    height: 16px;
}

.modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    align-items: center;
    background: var(--bg-tertiary);
}

.prompt-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid var(--border-secondary);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: var(--transition-fast);
}

.prompt-textarea:focus {
    outline: none;
    border-color: var(--accent-secondary);
    box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.1);
}

.prompt-tips {
    margin-top: 16px;
    padding: 12px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.prompt-tips p {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.prompt-tips p:last-child {
    margin-bottom: 0;
}

.btn {
    padding: 8px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
}


.config-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.config-item-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 10px;
    border: 1px solid var(--border-primary);
    transition: var(--transition-fast);
}

.config-item-row:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

/* 在recognition-type-config容器内的config-item-row样式调整 */
.recognition-type-config .config-item-row {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 0px 0;
    margin: 0;
}

.recognition-type-config .config-item-row:hover {
    background: transparent;
    box-shadow: none;
}

.config-item-info {
    flex: 1;
}

.config-item-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.config-item-desc {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.config-item-control {
    margin-left: 16px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-tertiary);
    transition: var(--transition-fast);
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--transition-fast);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: var(--success-text);
}

input:checked + .slider:before {
    transform: translateX(16px);
}

input:focus + .slider {
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

/* 快捷键配置样式 */
.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-primary);
    transition: var(--transition-fast);
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut-item:hover {
    background: var(--bg-tertiary);
    border-radius: 8px;
}

/* 可选配置项样式 - 类似快捷键配置的分割线样式 */
.config-option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-bottom: 1px solid var(--border-primary);
    transition: var(--transition-fast);
}

.config-option-item:last-child {
    border-bottom: none;
}

.config-option-item:hover {
    background: var(--bg-tertiary);
    margin: 0 -16px;
    padding: 16px;
    border-radius: 8px;
}

/* 子配置项样式（如历史记录数量设置） */
.config-option-item.config-sub-item {
    padding-left: 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-secondary);
}

.config-option-item.config-sub-item:hover {
    background: var(--bg-tertiary);
    margin: 0 -16px 0 4px;
    padding: 16px 16px 16px 36px;
}

.config-option-info {
    flex: 1;
    margin-right: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-option-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: help;
    transition: var(--transition-fast);
    border-radius: 4px;
    padding: 2px 4px;
    margin: -2px -4px;
}

.config-option-name[data-tooltip]:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.config-option-control {
    margin-left: 16px;
}

/* 历史记录配置项的多控件布局 */
.config-option-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 16px;
}

/* 配置项帮助按钮样式 */
.config-help-btn {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    min-height: 24px;
    position: relative;
}

.config-help-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: scale(1.05);
}

.config-help-btn .btn-icon {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    fill: none;
}

/* 自定义工具提示样式 */
.custom-tooltip {
    position: absolute;
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    line-height: 1.4;
    max-width: 280px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px var(--shadow-medium);
    border: 1px solid var(--border-primary);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
}

.custom-tooltip.show {
    opacity: 1;
    visibility: visible;
}

/* 工具提示箭头 - 指向下方（触发按钮） */
.custom-tooltip::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--border-primary);
    z-index: 1;
}

.custom-tooltip::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid var(--bg-secondary);
    z-index: 2;
}

.shortcut-info {
    flex: 1;
    margin-right: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.shortcut-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: help;
    transition: var(--transition-fast);
    border-radius: 4px;
    padding: 2px 4px;
    margin: -2px -4px;
}

.shortcut-name[data-tooltip]:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.shortcut-input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
}

input[type="text"].shortcut-input {
    width: 110px;
    padding: 6px 8px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

input[type="text"].shortcut-input:hover {
    border-color: var(--border-secondary);
    background: var(--bg-tertiary);
}

input[type="text"].shortcut-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background: var(--bg-primary);
}

input[type="text"].shortcut-input.recording {
    border-color: var(--warning-text);
    background: var(--warning-bg);
    animation: pulse-opacity 1.5s infinite;
}

input[type="text"].shortcut-input.conflict {
    border-color: var(--error-text);
    background: var(--error-bg);
}



@keyframes pulse-opacity {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* 配置界面底部导航栏 - 复刻主界面导航栏样式 */
.config-bottom-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--area-buttons);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    margin-top: 0px;
    gap: 4px;
}

/* 配置界面导航栏使用与主界面相同的左右分栏布局 */
.config-bottom-nav .left-controls,
.config-bottom-nav .right-controls {
    display: flex;
    align-items: center;
    gap: 8.5px;
}

/* 配置界面导航栏按钮使用与主界面相同的control-btn样式 */
.config-bottom-nav .control-btn {
    padding: 8px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: var(--transition-fast);
    min-width: 32px;
    min-height: 32px;
    box-sizing: border-box;
}

.config-bottom-nav .control-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

/* 只有图标的按钮（主页、主题切换）保持正方形 */
.config-bottom-nav .control-btn:not(:has(.btn-text)) {
    width: 32px;
    height: 32px;
    padding: 8px;
}

/* 有文字的按钮（基础配置、模型服务、历史记录）使用水平padding */
.config-bottom-nav .control-btn:has(.btn-text) {
    padding: 8px 12px;
    min-width: auto;
    min-height: 32px;
}

.config-bottom-nav .control-btn .btn-icon {
    width: 14px;
    height: 14px;
    color: currentColor;
}

.config-bottom-nav .control-btn .btn-icon svg {
    width: 100%;
    height: 100%;
    stroke: currentColor;
}

.config-bottom-nav .control-btn .btn-text {
    font-size: 11px;
}

/* 激活状态样式 - 复刻模型服务商列表的高亮效果 */
.config-bottom-nav .control-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

/* 激活状态的悬停效果 */
.config-bottom-nav .control-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

/* 激活状态下的图标颜色 */
.config-bottom-nav .control-btn.active .btn-icon {
    color: var(--primary-btn-text);
}

/* 激活状态下的文字颜色 */
.config-bottom-nav .control-btn.active .btn-text {
    color: var(--primary-btn-text);
}

/* 暗色主题下的激活状态样式 - 复刻模型服务商列表的高亮效果 */
[data-theme="dark"] .config-bottom-nav .control-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 暗色主题下激活状态的悬停效果 */
[data-theme="dark"] .config-bottom-nav .control-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 暗色主题下激活状态的图标颜色 */
[data-theme="dark"] .config-bottom-nav .control-btn.active .btn-icon {
    color: var(--primary-btn-text);
}

/* 暗色主题下激活状态的文字颜色 */
[data-theme="dark"] .config-bottom-nav .control-btn.active .btn-text {
    color: var(--primary-btn-text);
}

/* 自动主题下的激活状态样式 - 复刻模型服务商列表的高亮效果 */
[data-theme="auto"] .config-bottom-nav .control-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 自动主题下激活状态的悬停效果 */
[data-theme="auto"] .config-bottom-nav .control-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 自动主题下激活状态的图标颜色 */
[data-theme="auto"] .config-bottom-nav .control-btn.active .btn-icon {
    color: var(--primary-btn-text);
}

/* 自动主题下激活状态的文字颜色 */
[data-theme="auto"] .config-bottom-nav .control-btn.active .btn-text {
    color: var(--primary-btn-text);
}

/* 亮色主题下的激活状态样式 - 复刻模型服务商列表的高亮效果 */
[data-theme="light"] .config-bottom-nav .control-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

/* 亮色主题下激活状态的悬停效果 */
[data-theme="light"] .config-bottom-nav .control-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

/* 亮色主题下激活状态的图标颜色 */
[data-theme="light"] .config-bottom-nav .control-btn.active .btn-icon {
    color: var(--primary-btn-text);
}

/* 亮色主题下激活状态的文字颜色 */
[data-theme="light"] .config-bottom-nav .control-btn.active .btn-text {
    color: var(--primary-btn-text);
}

/* 保留原有nav-btn样式以兼容可能的遗留代码 */
.nav-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: var(--transition-fast);
    min-width: 32px;
    min-height: 32px;
    box-sizing: border-box;
}

.nav-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.nav-icon {
    width: 14px;
    height: 14px;
    color: currentColor;
}

.nav-icon svg {
    width: 100%;
    height: 100%;
    stroke: currentColor;
}

.nav-text {
    font-size: 11px;
}

.nav-btn:not(:has(.nav-text)) {
    width: 32px;
    height: 32px;
    padding: 8px;
}

.nav-btn:has(.nav-text) {
    padding: 8px 12px;
    min-width: auto;
    min-height: 32px;
}

/* 主题切换按钮通用样式 */
.theme-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-btn:hover {
    background: var(--bg-tertiary);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.config-section {
    margin-bottom: 20px;
}

.config-section:last-child {
    margin-bottom: 0;
}

.config-section h3 {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-section h3::before {
    content: '⚙️';
    font-size: 14px;
}

.config-section h4 {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 6px;
    padding: 6px 10px;
}

.config-section h5 {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    border-left: 3px solid var(--accent-primary);
}

.config-subsection {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
}

.config-subsection:last-child {
    margin-bottom: 0;
}

.form-help {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--text-secondary);
    font-style: italic;
}

/* 配置标题中的链接样式 */
.config-section h4 .config-link {
    color: #1976d2;
    text-decoration: none;
    font-size: inherit;
    font-weight: inherit;
    cursor: pointer;
    transition: color 0.2s ease;
}

.config-section h4 .config-link:hover {
    color: #1565c0;
    text-decoration: underline;
}

/* 暗色主题下的配置链接样式 */
[data-theme="dark"] .config-section h4 .config-link {
    color: #64b5f6;
}

[data-theme="dark"] .config-section h4 .config-link:hover {
    color: #42a5f5;
    text-decoration: underline;
}

/* 标签前图标通用样式 */
.label-icon {
    margin-right: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    vertical-align: middle;
}

.label-icon:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

.label-icon:active {
    transform: scale(0.95);
}

.label-icon:focus {
    outline: 2px solid var(--primary-color-alpha);
    outline-offset: 2px;
    border-radius: 2px;
}

/* API Key图标特定样式 */
.api-key-icon:hover {
    color: var(--success-text);
}

/* URL图标特定样式 */
.url-icon:hover {
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 16px;
}

/* AI大模型配置特殊间距优化 */
#llm-config .form-group {
    margin-bottom: 18px;
}

#llm-config .form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 13px;
}

/* AI大模型配置标签间距优化 */
#llm-config .form-group label {
    margin-bottom: 8px;
}

.select-input, input[type="text"], input[type="password"], input[type="number"], textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 13px;
    font-family: inherit;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.select-input:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="number"]:focus, textarea:focus {
    outline: none;
    border-color: var(--border-secondary);
    background: var(--bg-primary);
}

/* 配置项中的数字输入框样式 */
.number-input {
    width: 45px !important;
    height: 25px !important;
    text-align: center;
    padding: 0 4px !important;
    font-size: 12px !important;
    line-height: 20px !important;
    margin-left: 8px;
}

/* 隐藏数字输入框的调节按钮 */
.number-input::-webkit-outer-spin-button,
.number-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* uTools (Chromium 91) - 数字输入框样式统一 */
/* 注意：-moz-前缀在uTools中无效，已移除 */

/* 选择框统一样式 */
.select-input,
#llm-platform, #llm-model-select,
#ocr-service {
    cursor: pointer;
    appearance: none;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 8px 32px 8px 10px;
    font-size: 13px;
    font-family: inherit;
    position: relative;
}

/* 使用伪元素创建下拉箭头 */
.select-input::after,
#llm-platform::after, #llm-model-select::after,
#ocr-service::after {
    content: '▼';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 12px;
    pointer-events: none;
}

/* 选择框悬停效果 */
.select-input:hover,
#llm-platform:hover, #llm-model-select:hover,
#ocr-service:hover {
    border-color: var(--border-secondary);
    background-color: var(--bg-secondary);
}

.select-input:hover::after,
#llm-platform:hover::after, #llm-model-select:hover::after,
#ocr-service:hover::after {
    color: var(--text-primary);
}

/* 选择框焦点效果 */
.select-input:focus,
#llm-platform:focus, #llm-model-select:focus,
#ocr-service:focus {
    outline: none;
    border-color: var(--border-secondary);
    box-shadow: 0 0 0 2px var(--shadow-light);
    background-color: var(--bg-primary);
}

.select-input:focus::after,
#llm-platform:focus::after, #llm-model-select:focus::after,
#ocr-service:focus::after {
    color: var(--text-primary);
}

/* 暗色主题下的选择框适配 */
[data-theme="dark"] .select-input,
[data-theme="dark"] select {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

[data-theme="dark"] .select-input::after,
[data-theme="dark"] select::after {
    color: var(--text-secondary);
}

[data-theme="dark"] .select-input:hover,
[data-theme="dark"] select:hover {
    background: var(--bg-secondary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .select-input:hover::after,
[data-theme="dark"] select:hover::after {
    color: var(--text-primary);
}

[data-theme="dark"] .select-input:focus,
[data-theme="dark"] select:focus {
    background: var(--bg-primary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .select-input:focus::after,
[data-theme="dark"] select:focus::after {
    color: var(--text-primary);
}

/* 确保所有输入框在主题切换时正确适配 */
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] textarea {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

[data-theme="dark"] input[type="text"]:focus,
[data-theme="dark"] input[type="password"]:focus,
[data-theme="dark"] input[type="number"]:focus,
[data-theme="dark"] textarea:focus {
    background: var(--bg-primary);
    border-color: var(--border-secondary);
}

/* 特殊处理result-text和raw-result-text在暗色模式下的背景色 */
[data-theme="dark"] .result-text {
    background: var(--area-result) !important;
}

[data-theme="dark"] .result-text:focus {
    background: var(--area-result) !important;
}

[data-theme="dark"] .raw-result-text {
    background: transparent !important;
}

[data-theme="dark"] .raw-result-text:focus {
    background: transparent !important;
}

/* 占位符样式 */
input::placeholder, textarea::placeholder {
    color: var(--text-tertiary);
    opacity: 1;
}

textarea {
    resize: vertical;
    min-height: 60px;
    line-height: 1.5;
}

/* 模型选择相关样式 */
.model-selection {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 新的模型版本区域样式 */
.model-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px; /* 增加上边距，与上方表单项创建更明显的视觉分隔 */
    margin-bottom: 10px; /* 与其他form-group label保持一致 */
}

.model-header label {
    margin: 0;
    font-size: 13px; /* 与其他form-group label保持一致 */
    font-weight: 500;
    color: var(--text-primary);
}

.model-actions {
    display: flex;
    gap: 8px;
}

/* 模型管理按钮样式 - 参考导航栏按钮尺寸 */
.model-actions .btn-small {
    padding: 6px 10px; /* 减小高度，参考导航栏按钮 */
    font-size: 11px;
    min-height: 28px; /* 与导航栏按钮高度一致 */
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    white-space: nowrap;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 4px;
}

.model-actions .btn-small svg {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

/* 网络代理按钮状态样式 - 使用更高优先级的选择器 */
.model-actions .btn-small.proxy-btn-default {
    /* 默认状态 - 使用原有样式 */
}

.model-actions .btn-small.proxy-btn-warning {
    background: #fef3c7 !important;
    color: #d97706 !important;
    border-color: #fbbf24 !important;
    font-weight: 500;
}

.model-actions .btn-small.proxy-btn-warning:hover {
    background: #fbbf24 !important;
    border-color: #f59e0b !important;
    box-shadow: 0 2px 8px rgba(217, 119, 6, 0.2);
}

.model-actions .btn-small.proxy-btn-success {
    background: #dcfce7 !important;
    color: #166534 !important;
    border-color: #bbf7d0 !important;
    font-weight: 500;
}

.model-actions .btn-small.proxy-btn-success:hover {
    background: #bbf7d0 !important;
    border-color: #86efac !important;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.model-actions .btn-small.proxy-btn-error {
    background: #fef2f2 !important;
    color: #dc2626 !important;
    border-color: #fecaca !important;
    font-weight: 500;
}

.model-actions .btn-small.proxy-btn-error:hover {
    background: #fecaca !important;
    border-color: #fca5a5 !important;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
}

.model-list {
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-secondary);
    min-height: 120px;
    padding: 20px 12px 12px 12px; /* 调整padding：上20px 右12px 下12px 左12px，使上下间距视觉平衡 */
    max-height: 350px;
    overflow-y: auto;
}

.model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
}

.model-item:last-child {
    margin-bottom: 8px;
}

.model-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

.model-name {
    font-size: 13px;
    font-weight: 500;
    flex: 1;
    margin-right: 12px;
}

/* 模型图标样式 */

.model-icon svg {
    width: 16px;
    height: 16px;
    color: var(--text-primary);
    fill: currentColor;
}

/* 获取模型弹窗中的图标样式 */
.fetch-model-item .model-icon svg {
    width: 20px;
    height: 20px;
    color: var(--text-primary);
    fill: currentColor;
}

/* 已添加模型的图标颜色调整 */
.fetch-model-item.added .model-icon svg {
    color: #1e40af !important;
    fill: currentColor !important;
}

[data-theme="dark"] .fetch-model-item.added .model-icon svg {
    color: #e5e7eb !important;
    fill: currentColor !important;
}

/* 悬停时的图标效果 - 已取消动画 */
/* .model-item:hover .model-icon svg,
.fetch-model-item:hover .model-icon svg {
    transform: scale(1.05);
} */

.model-item-actions {
    display: flex;
    gap: 6px;
}

.model-test-btn {
    /* 复刻翻译模型选择器的基础样式 */
    padding: 6px 10px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s ease;
    min-height: 28px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.model-test-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

/* 模型测试状态样式 - 保留状态颜色逻辑，采用新样式基础 */
.model-test-btn.test-status-untested {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
    font-weight: 500;
}

.model-test-btn.test-status-untested:hover {
    background: #4b5563;
    border-color: #4b5563;
    box-shadow: 0 2px 8px rgba(75, 85, 99, 0.3);
}

.model-test-btn.test-status-success {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
    font-weight: 500;
}

.model-test-btn.test-status-success:hover {
    background: #bbf7d0;
    border-color: #86efac;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.model-test-btn.test-status-failed {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
    font-weight: 500;
}

.model-test-btn.test-status-failed:hover {
    background: #fecaca;
    border-color: #fca5a5;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
}

/* 模型删除按钮样式 - 参考导航栏主题按钮 */
.model-delete-btn {
    padding: 4px;
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.model-delete-btn:hover {
    background: var(--bg-tertiary);
    color: var(--error-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.model-delete-btn svg {
    width: 14px;
    height: 14px;
}

.model-list-empty {
    text-align: center;
    color: var(--text-secondary);
    font-size: 13px;
    padding: 20px;
    font-style: italic;
}

/* 模型获取弹窗样式 - 与模型版本列表保持一致的间距 */
.fetch-models-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-primary);
    border-radius: 8px; /* 调整为与.model-list一致的圆角 */
    background: var(--bg-secondary); /* 调整为与.model-list一致的背景色 */
    padding: 12px; /* 添加容器内边距，与.model-list保持一致 */
}

.fetch-model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px; /* 调整为与.model-item一致的内边距 */
    margin-bottom: 8px; /* 添加列表项间距，与.model-item保持一致 */
    background: var(--bg-primary); /* 调整背景色，与.model-item保持一致 */
    border: 1px solid var(--border-primary); /* 添加边框，与.model-item保持一致 */
    border-radius: 6px; /* 添加圆角，与.model-item保持一致 */
}

.fetch-model-item:last-child {
    margin-bottom: 8px; /* 最后一项保持相同下边距 */
}

.fetch-model-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary); /* 添加悬停时的边框颜色变化 */
}

.fetch-model-name {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
    margin-right: 12px;
}

.add-model-btn {
    width: 24px;
    height: 24px;
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.add-model-btn:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.add-model-btn svg {
    width: 16px;
    height: 16px;
}

/* 移除模型按钮样式 */
.remove-model-btn {
    width: 24px;
    height: 24px;
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-model-btn:hover {
    background: var(--error-text);
    color: white;
    border-color: var(--error-text);
}

.remove-model-btn svg {
    width: 16px;
    height: 16px;
}

/* 已添加模型的高亮样式 - 调整为与新布局兼容 */
.fetch-model-item.added {
    background: #c1d5fe !important; /* 使用!important确保覆盖基础样式 */
    border-color: #3b82f6 !important; /* 调整边框颜色使其更明显 */
}

.fetch-model-item.added .fetch-model-name {
    color: #1e40af !important; /* 确保已添加模型的文字在亮色主题下清晰可见 */
}

[data-theme="dark"] .fetch-model-item.added {
    background: #374151 !important;
    border-color: #60a5fa !important; /* 调整暗色主题下的边框颜色 */
}

[data-theme="dark"] .fetch-model-item.added .fetch-model-name {
    color: #e5e7eb !important; /* 确保已添加模型的文字在暗色主题下清晰可见 */
}

/* 添加模型弹窗样式 */
.add-model-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 12px;
}

.add-model-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

.add-model-hint {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 8px;
    line-height: 1.4;
}

/* 传统模型选择框样式已移除，现在使用新的模型列表UI */

/* 地域选择组件样式 - 与其他下拉菜单组件保持一致 */
.region-select-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

.region-select-btn {
    width: 100%;
    background: var(--area-buttons);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 13px;
    font-family: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;
    min-height: 32px;
    box-sizing: border-box;
}

.region-select-btn:hover {
    border-color: var(--border-secondary);
    background-color: var(--bg-secondary);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.region-select-btn:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.region-text {
    flex: 1;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.region-arrow {
    font-size: 10px;
    color: var(--text-secondary);
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

.region-select-btn.active .region-arrow {
    transform: rotate(180deg);
}

.region-select-menu {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: var(--area-buttons);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 -8px 24px var(--shadow-medium);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
    margin-bottom: 4px;
    scrollbar-width: thin;
    scrollbar-color: var(--text-tertiary) transparent;
}

/* 地域选择菜单滚动条样式 */
.region-select-menu::-webkit-scrollbar {
    width: 6px;
}

.region-select-menu::-webkit-scrollbar-track {
    background: transparent;
}

.region-select-menu::-webkit-scrollbar-thumb {
    background-color: var(--text-tertiary);
    border-radius: 3px;
    border: none;
}

.region-select-menu::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

.region-select-menu.show {
    display: block;
}

.region-option {
    padding: 8px 12px;
    cursor: pointer;
    color: var(--text-primary);
    font-size: 13px;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-light);
}

.region-option:last-child {
    border-bottom: none;
}

.region-option:hover {
    background: var(--bg-secondary);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

.region-option.selected {
    background-color: var(--primary-color-alpha);
    color: var(--primary-color);
    font-weight: 500;
}

.region-option.selected:hover {
    background: rgba(25, 118, 210, 0.12);
    transform: translateX(2px);
}



.model-info {
    /* 使用更接近背景的颜色，减少视觉差异 */
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 14px;
    margin-top: 12px;
    margin-bottom: 16px;
    /* 添加非常轻微的阴影来区分区域 */
    box-shadow: 0 1px 3px var(--shadow-light);
}

.model-info h5 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.model-info .model-detail {
    display: flex;
    justify-content: space-between;
    margin: 4px 0;
    font-size: 13px;
}

.model-info .model-detail .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.model-info .model-detail .value {
    color: var(--text-primary);
    /* 让值的文字稍微突出一点 */
    font-weight: 500;
}

/* 暗色主题下的模型信息适配 */
[data-theme="dark"] .model-info {
    background: var(--bg-primary);
    border-color: var(--border-primary);
    box-shadow: 0 1px 3px var(--shadow-light);
}

/* 刷新按钮样式 */
.btn-small {
    padding: 8px 12px;
    font-size: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    white-space: nowrap;
    flex-shrink: 0;
}

.btn-small:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

.btn-small:active {
    background: var(--bg-primary);
    transform: translateY(1px);
}

/* OCR测试按钮样式 */
.ocr-test-btn {
    position: relative;
    transition: all 0.2s ease;
}

.ocr-test-btn.testing {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.ocr-test-btn.testing .test-btn-text::after {
    content: '...';
    animation: dots 1.5s infinite;
}

.ocr-test-btn.test-success {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.ocr-test-btn.test-error {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

/* 翻译测试按钮样式 - 复用OCR测试按钮样式 */
.translate-test-btn {
    position: relative;
    transition: all 0.2s ease;
}

.translate-test-btn.testing {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.translate-test-btn.testing .test-btn-text::after {
    content: '...';
    animation: dots 1.5s infinite;
}

/* 腾讯云配置标题样式 - 支持测试按钮在右侧 */
.tencent-config-title {
    position: relative;
    /* 保持与其他服务商一致的h4样式 */
}

/* 腾讯云测试按钮在标题内的定位 */
.tencent-config-title .traditional-test-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

/* 传统服务商测试按钮样式 - 参考AI大模型测试按钮 */
.traditional-test-btn {
    padding: 6px 10px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s ease;
    min-height: 28px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.traditional-test-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

/* 传统服务商测试按钮状态样式 - 与AI大模型测试按钮保持一致 */
.traditional-test-btn.test-status-untested {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
    font-weight: 500;
}

.traditional-test-btn.test-status-untested:hover {
    background: #4b5563;
    border-color: #4b5563;
    box-shadow: 0 2px 8px rgba(75, 85, 99, 0.3);
}

.traditional-test-btn.test-status-success {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
    font-weight: 500;
}

.traditional-test-btn.test-status-success:hover {
    background: #bbf7d0;
    border-color: #86efac;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.traditional-test-btn.test-status-failed {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
    font-weight: 500;
}

.traditional-test-btn.test-status-failed:hover {
    background: #fecaca;
    border-color: #fca5a5;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
}

/* 测试中状态样式 */
.traditional-test-btn.testing {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.traditional-test-btn.testing .test-btn-text::after {
    content: '...';
    animation: dots 1.5s infinite;
}

.translate-test-btn.test-success {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.translate-test-btn.test-error {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

@keyframes dots {
    0%, 20% { content: '...'; }
    40% { content: ''; }
    60% { content: '.'; }
    80% { content: '..'; }
    100% { content: '...'; }
}



.form-hint {
    display: block;
    margin-top: 6px;
    font-size: 12px;
    color: var(--text-tertiary);
    line-height: 1.4;
}



.loading-models {
    color: var(--text-secondary);
    font-style: italic;
}

.error-models {
    color: var(--error-text);
    font-size: 12px;
}

/* 复选框样式优化 */
input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    /* 自定义复选框样式，更加低调 */
    appearance: none;
    border: 1px solid var(--border-secondary);
    border-radius: 3px;
    background: var(--bg-primary);
    cursor: pointer;
    position: relative;
    /* 关键：垂直对齐方式 */
    vertical-align: middle;
    /* 微调位置确保与文字完美对齐 */
    margin-top: 0;
    margin-bottom: 0;
    flex-shrink: 0;
}



input[type="checkbox"]:hover {
    border-color: var(--text-secondary);
    background: var(--bg-secondary);
}

/* 禁用checkbox-btn内部复选框的悬停效果，避免与按钮悬停效果冲突 */
.checkbox-btn input[type="checkbox"]:hover {
    border-color: var(--border-primary);
    background: var(--bg-primary);
}

/* 确保checkbox-btn内部复选框选中时保持正确尺寸 */
.checkbox-btn input[type="checkbox"]:checked {
    background: var(--text-secondary);
    border-color: var(--text-secondary);
    width: 12px;
    height: 12px;
}

input[type="checkbox"]:checked {
    background: var(--text-secondary);
    border-color: var(--text-secondary);
    /* 确保选中时尺寸不变 */
    width: 16px;
    height: 16px;
}

input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--bg-primary);
    font-size: 10px;
    font-weight: bold;
}

/* 暗色主题下的复选框适配 */
[data-theme="dark"] input[type="checkbox"]:checked {
    background: var(--text-secondary);
    border-color: var(--text-secondary);
}

[data-theme="dark"] input[type="checkbox"]:checked::after {
    color: var(--bg-primary);
}

/* 暗色主题下checkbox-btn内部复选框选中时保持正确尺寸 */
[data-theme="dark"] .checkbox-btn input[type="checkbox"]:checked {
    background: var(--text-secondary);
    border-color: var(--text-secondary);
    width: 12px;
    height: 12px;
}

label input[type="checkbox"] {
    margin-right: 8px;
}



/* API Key输入框和切换按钮样式 */
.input-with-toggle {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-toggle input {
    flex: 1;
    padding-right: 45px; /* 为按钮留出空间 */
}

.toggle-password {
    position: absolute;
    right: 8px;
    top: 50%;
    background: none;
    border: none;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    margin-top: -16px;
    transition: var(--transition-fast);
    color: var(--text-secondary);
}

.toggle-password:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    transform: scale(1.05);
}

.toggle-password:active {
    background-color: var(--border-primary);
    transform: scale(0.95);
}

.eye-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    transition: var(--transition-fast);
}

/* API Base URL输入框和重置按钮样式 */
.input-with-reset {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-reset input {
    flex: 1;
    padding-right: 45px; /* 为重置按钮留出空间 */
}

.reset-url-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    background: none;
    border: none;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    margin-top: -16px;
    transition: var(--transition-fast);
    color: var(--text-secondary);
}

.reset-url-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
    transform: scale(1.05) rotate(180deg);
}

.reset-url-btn:active {
    background-color: var(--border-primary);
    transform: scale(0.95) rotate(360deg);
}

.reset-url-btn svg {
    width: 16px;
    height: 16px;
    transition: var(--transition-fast);
}

/* SVG图标样式 */
.eye-icon svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    fill: none;
    transition: var(--transition-fast);
}

/* 密码隐藏状态 */
.toggle-password.hidden {
    opacity: 0.7;
}

.toggle-password.hidden:hover {
    opacity: 1;
}

/* 密码显示状态 */
.toggle-password.visible {
    opacity: 1;
    color: var(--text-primary);
}

.toggle-password.visible:hover {
    color: var(--accent-secondary);
}

.config-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--border-primary);
}

/* 重复的.btn样式已移除，使用前面定义的统一样式 */

/* 置信度显示 */
.confidence-info {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.confidence-info.high {
    background: var(--success-bg);
    color: var(--success-text);
}

.confidence-info.medium {
    background: var(--warning-bg);
    color: var(--warning-text);
}

.confidence-info.low {
    background: var(--error-bg);
    color: var(--error-text);
}

/* 主题切换图标样式 */
.theme-icon {
    width: 14px;
    height: 14px;
    display: inline-block;
    vertical-align: middle;
    transition: all 0.2s ease;
    color: currentColor;
}

.theme-icon svg {
    width: 100%;
    height: 100%;
    stroke: currentColor;
}

/* 确保所有SVG图标都能正确继承颜色 */
svg {
    color: inherit;
}

/* 确保按钮中的SVG图标正确显示 */
button svg {
    pointer-events: none;
    display: block;
}

/* 帮助图标样式 */
.help-icon {
    position: relative;
    width: 13px;
    height: 13px;
    display: inline-block;
    vertical-align: middle;
    border: 1px solid var(--text-secondary);
    border-radius: 50%;
    transition: all 0.2s ease;
}

.help-icon::before {
    content: '?';
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-secondary);
    font-size: 9px;
    font-weight: bold;
    font-family: var(--font-family);
}

.help-icon:hover {
    border-color: var(--text-primary);
}

.help-icon:hover::before {
    color: var(--text-primary);
}

/* 简单的悬停效果 */
.header-btn:hover .theme-icon,
.control-btn:hover .theme-icon,
.theme-btn:hover .theme-icon {
    transform: scale(1.1);
}

.help-btn:hover .help-icon {
    transform: scale(1.1);
}

/* ==================== 历史记录界面样式 ==================== */

/* 历史记录类型切换按钮 */
.history-type-buttons {
    display: flex;
    gap: 8px;
    padding: 12px;
    background: var(--area-preview);
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    margin-bottom: 12px;
}

.history-type-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    font-weight: 500;
}

.history-type-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

/* 激活状态样式 - 复刻导航栏按钮的高亮效果 */
.history-type-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

.history-type-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

.history-type-btn .btn-icon {
    width: 16px;
    height: 16px;
    stroke: #007AFF; /* 蓝色图标 */
    fill: none;
}

.history-type-btn .btn-text {
    font-size: 13px;
    font-weight: 500;
}

/* 暗色主题下的历史记录类型按钮适配 */
[data-theme="dark"] .history-type-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .history-type-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 亮色主题下的历史记录类型按钮适配 */
[data-theme="light"] .history-type-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

[data-theme="light"] .history-type-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

/* 自动主题下的历史记录类型按钮适配 */
[data-theme="auto"] .history-type-btn.active {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="auto"] .history-type-btn.active:hover {
    background: var(--primary-btn-hover);
    color: var(--primary-btn-text);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 历史记录列表区域 */
.history-list-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--area-preview);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    margin-bottom: 0px;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 6px 0;
}

.history-list::-webkit-scrollbar {
    display: none;
}

/* 历史记录项 - 极简设计 */
.history-item {
    padding: 8px 14px;
    margin: 0 10px;
    background: transparent;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 历史记录项内容区域 */
.history-item-content {
    flex: 1;
    min-width: 0; /* 允许内容收缩 */
}

/* 选中状态下的删除按钮 */
.history-delete-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: color 0.2s ease;
}

.history-delete-btn svg {
    width: 12px;
    height: 12px;
    stroke: currentColor;
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.history-delete-btn:hover {
    color: var(--text-primary);
}

/* 亮色主题下的删除按钮适配 */
[data-theme="light"] .history-delete-btn {
    color: #999999; /* 淡灰色 */
}

[data-theme="light"] .history-delete-btn:hover {
    color: #666666; /* 悬停时稍深一些的灰色 */
}

/* 暗色主题下的删除按钮适配 */
[data-theme="dark"] .history-delete-btn {
    color: #cccccc; /* 暗色模式下的浅灰色 */
}

[data-theme="dark"] .history-delete-btn:hover {
    color: #ffffff; /* 暗色模式下悬停时为白色 */
}

.history-item.active .history-delete-btn {
    display: flex;
}

/* 选中状态下压缩文字空间 */
.history-item.active .history-item-content {
    padding-right: 32px; /* 为删除按钮留出空间 */
}

.history-item:hover {
    background: var(--bg-tertiary);
}

.history-item.active {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.history-item.active .history-time,
.history-item.active .history-preview {
    color: var(--text-primary);
}

/* 识别文字预览 - 单行显示 */
.history-preview {
    font-size: 13px;
    color: var(--text-primary);
    line-height: 1.4;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 时间信息行 - 移到下方 */
.history-meta-line {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 0;
}

.history-time {
    font-size: 10px;
    color: var(--text-secondary);
    flex-shrink: 0;
}

/* 历史记录空状态 */
.history-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
    /* SVG图标样式 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-icon svg {
    width: 48px;
    height: 48px;
    color: var(--text-secondary);
    opacity: 0.6;
}

.empty-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.empty-hint {
    font-size: 14px;
    opacity: 0.8;
}

/* 历史详情区域 - 与翻译结果文本框样式完全一致 */
.history-detail-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1; /* 确保占满父容器 */
    color: var(--text-secondary);
    text-align: center;
    min-height: 0;
    /* 与翻译结果文本框保持完全一致的样式 */
    background: var(--area-result);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 16px; /* 与文本框内边距一致 */
    box-sizing: border-box; /* 与文本框盒模型一致 */
    /* 移除外边距和阴影，确保布局一致性 */
}

.detail-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
    /* SVG图标样式 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.detail-empty-icon svg {
    width: 48px;
    height: 48px;
    color: var(--text-secondary);
    opacity: 0.6;
}

.detail-empty-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
}

/* 为空状态添加提示文本 */
.detail-empty-hint {
    font-size: 14px;
    color: var(--text-tertiary);
    margin-top: 8px;
    opacity: 0.8;
}

/* 空状态焦点样式 - 与翻译结果文本框保持一致 */
.history-detail-empty:focus {
    outline: none;
    border-color: var(--border-secondary);
    background: var(--area-result);
}

/* 历史记录数量统计组件 - 清晰简洁版本 */
.history-count-indicator {
    position: absolute;
    bottom: 12px;
    left: 50%;
    transform: translateX(-50%) scale(0.9);
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 10px; /* 稍微增大圆角 */
    padding: 6px 10px; /* 稍微增大内边距 */
    font-size: 11px; /* 稍微增大字体 */
    font-weight: 500;
    color: var(--text-secondary);
    z-index: 10;
    pointer-events: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 清晰的阴影，无模糊效果 */
    opacity: 0;
    transition: all 1s ease-out;
    /* 确保没有任何模糊或透明度干扰 */
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
}

/* 鼠标悬停在左侧面板时显示统计组件 - 清晰简洁版本 */
#history-view .left-panel:hover .history-count-indicator {
    opacity: 1;
    transform: translateX(-50%) scale(0.95); /* 稍微增大的悬停状态 */
    transition: all 0.3s ease-out;
}

/* 历史记录页面特定样式修复 */
#history-view .main-container {
    /* 确保主容器正确占据全屏高度 */
    height: 100vh;
    display: flex;
    flex-direction: column;
}

#history-view .content-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    min-height: 0;
}

#history-view .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    /* 确保右侧面板正确处理底部导航栏的空间 */
    position: relative;
    padding: 10px; /* 与左侧面板保持一致的内边距 */
}

#history-view .result-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    /* 重要：为复制按钮提供定位上下文 */
    position: relative;
    /* 与主OCR页面保持完全一致的间距 */
    margin-bottom: 12px;
    margin-top: 0px;
    overflow: hidden;
}

/* 历史记录页面底部导航栏 - 回归正常流式布局 */
#history-view .config-bottom-nav {
    /* 移除绝对定位，使用正常的流式布局 */
    margin-top: 0;
    /* 与主页面保持一致的样式 */
}

/* 历史记录结果显示区域 */
.history-result-area {
    position: relative;
    flex: 1; /* 确保占满父容器的剩余空间 */
    display: flex; /* 关键：必须设置为flex */
    flex-direction: column;
    overflow: hidden;
    /* 移除 min-height: 0，与 single-result-container 保持一致 */
}

/* OCR结果区域特定样式 - 优化字体配置并确保与主页面一致 */
#ocr-result-area .result-text {
    flex: 1;
    background: var(--area-result); /* 复刻OCR结果区域背景 */
    border: 1px solid var(--border-primary); /* 复刻OCR结果区域边框 */
    border-radius: 12px; /* 复刻OCR结果区域圆角 */
    padding: 16px; /* 复刻OCR结果区域内边距 */
    font-family: var(--font-family); /* 统一字体族 */
    font-size: var(--font-size-lg); /* 从md提升到lg，增大历史记录可读性 */
    line-height: var(--line-height-relaxed); /* 宽松行高，便于长文本阅读 */
    font-weight: var(--font-weight-normal); /* 正常字重 */
    white-space: pre-wrap; /* 保持原始格式 */
    word-wrap: break-word; /* 长单词换行 */
    resize: none;
    width: 100%;
    box-sizing: border-box;
    color: var(--text-primary);
    overflow-y: auto;
    /* 改善历史记录显示效果 */
    letter-spacing: 0.01em; /* 轻微增加字间距，提升长文本可读性 */
    /* 隐藏滚动条但保持滚动功能 - 复刻OCR结果区域 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

#ocr-result-area .result-text::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

#ocr-result-area .result-text:focus {
    outline: none;
    border-color: var(--border-secondary); /* 复刻OCR结果区域焦点样式 */
    background: var(--area-result); /* 复刻OCR结果区域焦点样式 */
}

/* 翻译记录双栏显示 - 复刻主OCR页面Markdown模式的分栏样式 */
.translate-dual-pane {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 16px; /* 复刻dual-result-container的gap */
}



.translate-pane {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative; /* 为每个分栏的复制按钮提供定位上下文 */
}

/* 翻译记录文本框样式 - 优化字体配置并保持与主页面一致 */
.translate-dual-pane .result-text {
    flex: 1;
    background: var(--area-result); /* 复刻OCR结果区域背景 */
    border: 1px solid var(--border-primary); /* 复刻OCR结果区域边框 */
    border-radius: 12px; /* 复刻OCR结果区域圆角 */
    padding: 16px; /* 复刻OCR结果区域内边距 */
    font-family: var(--font-family); /* 统一字体族 */
    font-size: var(--font-size-lg); /* 从md提升到lg，增大字号便于对比阅读 */
    line-height: var(--line-height-relaxed); /* 宽松行高，提升双栏对比体验 */
    font-weight: var(--font-weight-normal); /* 正常字重 */
    white-space: pre-wrap; /* 保持原始格式 */
    word-wrap: break-word; /* 长单词换行 */
    resize: none;
    width: 100%;
    box-sizing: border-box;
    color: var(--text-primary);
    overflow-y: auto;
    min-height: 0; /* 确保可以正确收缩 */
    /* 改善双栏对比阅读体验 */
    letter-spacing: 0.01em; /* 轻微增加字间距，便于对比阅读 */
    /* 隐藏滚动条但保持滚动功能 - 复刻OCR结果区域 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.translate-dual-pane .result-text::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.translate-dual-pane .result-text:focus {
    outline: none;
    border-color: var(--border-secondary); /* 复刻OCR结果区域焦点样式 */
    background: var(--area-result); /* 复刻OCR结果区域焦点样式 */
}

/* 暗色主题适配 - 清晰简洁版本 */
[data-theme="dark"] .history-count-indicator {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15); /* 清晰的暗色主题阴影 */
}

/* 自动主题适配 */
@media (prefers-color-scheme: dark) {
    [data-theme="auto"] .history-count-indicator {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15); /* 清晰的暗色主题阴影 */
    }

    /* 翻译模型选择器自动主题适配 */
    [data-theme="auto"] .translate-model-btn {
        background: var(--bg-secondary);
        border-color: var(--border-primary);
        color: var(--text-primary);
    }

    [data-theme="auto"] .translate-model-btn:hover {
        background: var(--bg-tertiary);
        border-color: var(--border-secondary);
    }

    [data-theme="auto"] .translate-model-btn.active {
        /* 复刻主页面模型选择在自动主题下的高亮效果 */
        background: rgba(25, 118, 210, 0.15);
        color: #64b5f6;
        font-weight: 500;
        border-color: rgba(25, 118, 210, 0.3);
    }

    [data-theme="auto"] .translate-model-btn.active:hover {
        /* 复刻主页面模型选择在自动主题下的选中悬停效果 */
        background: rgba(25, 118, 210, 0.2);
        color: #64b5f6;
        border-color: rgba(25, 118, 210, 0.4);
    }

    /* 自动主题下的模型测试按钮 */
    [data-theme="auto"] .model-test-btn {
        background: var(--bg-secondary);
        border-color: var(--border-primary);
        color: var(--text-primary);
    }

    [data-theme="auto"] .model-test-btn:hover {
        background: var(--bg-tertiary);
        border-color: var(--border-secondary);
    }

    /* 自动主题下的测试状态样式 */
    [data-theme="auto"] .model-test-btn.test-status-untested {
        background: #6b7280;
        color: white;
        border-color: #6b7280;
    }

    [data-theme="auto"] .model-test-btn.test-status-untested:hover {
        background: #4b5563;
        border-color: #4b5563;
        box-shadow: 0 2px 8px rgba(75, 85, 99, 0.4);
    }

    [data-theme="auto"] .model-test-btn.test-status-success {
        background: rgba(34, 197, 94, 0.15);
        color: #4ade80;
        border-color: rgba(34, 197, 94, 0.3);
    }

    [data-theme="auto"] .model-test-btn.test-status-success:hover {
        background: rgba(34, 197, 94, 0.2);
        border-color: rgba(34, 197, 94, 0.4);
        box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
    }

    [data-theme="auto"] .model-test-btn.test-status-failed {
        background: rgba(239, 68, 68, 0.15);
        color: #f87171;
        border-color: rgba(239, 68, 68, 0.3);
    }

    [data-theme="auto"] .model-test-btn.test-status-failed:hover {
        background: rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.4);
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    }

    /* 自动主题下的网络代理按钮状态样式 */
    [data-theme="auto"] .model-actions .btn-small.proxy-btn-warning {
        background: rgba(217, 119, 6, 0.15) !important;
        color: #f59e0b !important;
        border-color: rgba(217, 119, 6, 0.3) !important;
    }

    [data-theme="auto"] .model-actions .btn-small.proxy-btn-warning:hover {
        background: rgba(217, 119, 6, 0.2) !important;
        border-color: rgba(217, 119, 6, 0.4) !important;
        box-shadow: 0 2px 8px rgba(217, 119, 6, 0.3);
    }

    [data-theme="auto"] .model-actions .btn-small.proxy-btn-success {
        background: rgba(34, 197, 94, 0.15) !important;
        color: #4ade80 !important;
        border-color: rgba(34, 197, 94, 0.3) !important;
    }

    [data-theme="auto"] .model-actions .btn-small.proxy-btn-success:hover {
        background: rgba(34, 197, 94, 0.2) !important;
        border-color: rgba(34, 197, 94, 0.4) !important;
        box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
    }

    [data-theme="auto"] .model-actions .btn-small.proxy-btn-error {
        background: rgba(239, 68, 68, 0.15) !important;
        color: #f87171 !important;
        border-color: rgba(239, 68, 68, 0.3) !important;
    }

    [data-theme="auto"] .model-actions .btn-small.proxy-btn-error:hover {
        background: rgba(239, 68, 68, 0.2) !important;
        border-color: rgba(239, 68, 68, 0.4) !important;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    }
}

/* 导航栏活跃状态 */
.nav-btn.active {
    background: var(--accent-color);
    color: white;
}

.nav-btn.active .nav-icon,
.nav-btn.active .nav-text {
    color: white;
}

/* 翻译页面样式 */
.translate-container {
    flex-direction: column;
    gap: 0;
    height: 100%;
    padding: 10px; /* 与右侧面板一致的边距 */
}

.translate-input-section,
.translate-result-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.translate-input-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 12px; /* 与OCR页面result-content的margin-bottom一致 */
}

.translate-result-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: 12px; /* 与导航栏保持一致的间距 */
    min-height: 0; /* 确保flex子元素可以正确收缩 */
    height: 100%; /* 确保高度填满父容器 */
    position: relative; /* 为绝对定位的操作按钮提供定位上下文 */
}

.translate-textarea {
    flex: 1;
    background: var(--area-result); /* 复刻OCR结果区域背景 */
    border: 1px solid var(--border-primary); /* 复刻OCR结果区域边框 */
    border-radius: 12px; /* 复刻OCR结果区域圆角 */
    padding: 16px; /* 复刻OCR结果区域内边距 */
    font-family: var(--font-family); /* 统一字体族 */
    font-size: var(--font-size-lg); /* 从md提升到lg，增大字号提升输入体验 */
    line-height: var(--line-height-relaxed); /* 宽松行高，便于阅读和编辑 */
    font-weight: var(--font-weight-normal); /* 正常字重 */
    white-space: pre-wrap; /* 保持换行格式 */
    word-wrap: break-word; /* 长单词换行 */
    resize: none;
    width: 100%;
    box-sizing: border-box;
    color: var(--text-primary);
    overflow-y: auto;
    /* 改善文本输入体验 */
    letter-spacing: 0.01em; /* 轻微增加字间距 */
    /* 隐藏滚动条但保持滚动功能 - 复刻OCR结果区域 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.translate-textarea::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.translate-textarea:focus {
    outline: none;
    border-color: var(--border-secondary); /* 复刻OCR结果区域焦点样式 */
    background: var(--area-result); /* 复刻OCR结果区域焦点样式 */
}

.translate-textarea::placeholder {
    color: var(--text-tertiary);
}

.translate-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px; /* 复刻OCR页面result-controls的padding */
    background: var(--area-buttons); /* 复刻OCR页面result-controls的背景 */
    border-radius: 8px; /* 复刻OCR页面result-controls的圆角 */
    border: 1px solid var(--border-primary); /* 复刻OCR页面result-controls的边框 */
    gap: 3px; /* 与OCR页面result-controls的gap保持一致 */
    min-height: 48px; /* 确保与OCR页面导航栏高度一致 */
}

.translate-controls .left-controls,
.translate-controls .center-controls,
.translate-controls .right-controls {
    display: flex;
    align-items: center;
    gap: 6.5px; /* 与OCR页面控制区域的gap完全一致 */
}

/* 语言选择器容器 */
.language-selector-container {
    display: flex;
    align-items: center;
    gap: 0px; /* 语言选择器和交换按钮之间的间距 */
    padding: 0px; /* 移除额外的内边距，让center-controls的gap来控制间距 */
    background: transparent;
    border: none;
    border-radius: 6px;
}

/* 语言选择器 */
.language-selector {
    position: relative;
}

.language-select {
    appearance: none;
    background: #e6f3ff;
    color: #1e40af;
    border: 1px solid #60a5fa;
    border-radius: 8px;
    padding: 8px 10px; /* 左右边距从8px调整为10px，增加文字与边框的距离 */
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    width: 100px !important; /* 强制固定宽度，防止JavaScript动态修改 */
    height: 32px; /* 与控制按钮和翻译模型按钮保持一致的高度 */
    outline: none;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出文本 */
    text-overflow: ellipsis; /* 溢出时显示省略号 */
    box-sizing: border-box; /* 确保高度计算包含边框和内边距 */
    font-family: var(--font-emoji); /* 国旗字体支持 */
}

/* 语言选择器按钮内的文本容器 */
.language-select .language-text {
    display: flex;
    align-items: center;
    gap: 6px; /* 与下拉菜单保持一致的间距 */
    width: 100%;
    justify-content: center;
}

/* 语言选择器按钮内的emoji样式 */
.language-select .language-emoji {
    flex-shrink: 0;
    font-size: 14px;
    width: 16px;
    text-align: center;
    font-family: 'Twemoji Country Flags', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

/* 语言选择器按钮内的文本样式 */
.language-select .language-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
}

/* 语言选择器下拉菜单 - 完全复刻 .model-select-menu */
.language-select-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100px; /* 与按钮宽度保持一致 */
    background: var(--area-buttons);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 8px 24px var(--shadow-medium);
    z-index: 1000;
    max-height: 250px;
    overflow-y: auto;
    display: none;
    margin-top: 4px;
    scrollbar-width: thin;
    scrollbar-color: var(--text-tertiary) transparent;
}

/* 语言选择菜单滚动条样式 - 完全复刻 .model-select-menu 滚动条 */
.language-select-menu::-webkit-scrollbar {
    width: 6px;
}

.language-select-menu::-webkit-scrollbar-track {
    background: transparent;
}

.language-select-menu::-webkit-scrollbar-thumb {
    background-color: var(--text-tertiary);
    border-radius: 3px;
    border: none;
}

.language-select-menu::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

.language-select-menu.show {
    display: block;
}

/* 语言选择器按钮内的文本样式 */
.language-select .language-text {
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出文本 */
    text-overflow: ellipsis; /* 溢出时显示省略号 */
    width: 100%; /* 占满按钮宽度 */
    text-align: center; /* 文本居中 */
}

.language-select:hover {
    background: #dbeafe;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

.language-select:focus {
    outline: none;
    background: #dbeafe;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

/* 语言选项样式 - 完全复刻 .model-option */
.language-option {
    padding: 8px 12px; /* 与 .model-option 完全相同 */
    cursor: pointer;
    font-size: 13px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px; /* emoji和文本之间的间距 */
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出文本 */
    width: 100%; /* 确保占满容器宽度 */
    box-sizing: border-box; /* 包含padding和border在内的宽度计算 */
}

/* 国旗emoji字体支持 */
@font-face {
    font-family: 'Twemoji Country Flags';
    unicode-range: U+1F1E6-1F1FF, U+1F3F4, U+E0062-E0063, U+E0065, U+E0067, U+E006C, U+E006E, U+E0073-E0074, U+E0077, U+E007F;
    src: url('https://cdn.jsdelivr.net/gh/beyondkmp/country-flag-emoji-polyfill@master/font/TwemojiCountryFlags.woff2') format('woff2');
    font-display: swap;
}

/* 响应式字体支持 - 保持增大后的字号比例 */
@media (max-width: 768px) {
    :root {
        --font-size-xxs: 7px;
        --font-size-xs: 9px;
        --font-size-sm: 10px;
        --font-size-normal: 11px;
        --font-size-base: 13px;  /* 保持增大的比例 */
        --font-size-md: 14px;    /* 保持增大的比例 */
        --font-size-lg: 15px;
        --font-size-xl: 17px;
        --font-size-xxl: 19px;
        --font-size-huge: 22px;
    }
}

@media (max-width: 480px) {
    :root {
        --font-size-xxs: 6px;
        --font-size-xs: 8px;
        --font-size-sm: 9px;
        --font-size-normal: 10px;
        --font-size-base: 12px;  /* 保持增大的比例 */
        --font-size-md: 13px;    /* 保持增大的比例 */
        --font-size-lg: 14px;
        --font-size-xl: 16px;
        --font-size-xxl: 18px;
        --font-size-huge: 20px;
    }
}

/* 响应式字体支持 */
@media (max-width: 768px) {
    :root {
        --font-size-xs: 11px;
        --font-size-sm: 12px;
        --font-size-base: 13px;
        --font-size-md: 15px;
        --font-size-lg: 17px;
        --font-size-xl: 19px;
    }
}

@media (max-width: 480px) {
    :root {
        --font-size-xs: 10px;
        --font-size-sm: 11px;
        --font-size-base: 12px;
        --font-size-md: 14px;
        --font-size-lg: 16px;
        --font-size-xl: 18px;
    }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}

/* 用户偏好设置支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 语言选项内的emoji样式 */
.language-option span:first-child {
    flex-shrink: 0; /* emoji不缩放 */
    font-size: 14px; /* emoji稍大一点 */
    width: 16px; /* 固定emoji宽度 */
    text-align: center; /* emoji居中 */
    font-family: 'Twemoji Country Flags', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif; /* 国旗字体支持 */
}

/* 语言选项内的文本样式 */
.language-option span:last-child {
    flex: 1; /* 文本占据剩余空间 */
    overflow: hidden; /* 隐藏溢出文本 */
    text-overflow: ellipsis; /* 溢出时显示省略号 */
    white-space: nowrap; /* 防止文本换行 */
}

.language-option:last-child {
    border-bottom: none;
}

.language-option:hover {
    background: var(--bg-secondary);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

/* 选中状态高亮 - 使用蓝色主题 */
.language-option.selected {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    font-weight: 500;
}

/* 选中项的悬停效果 - 使用蓝色主题 */
.language-option.selected:hover {
    background: rgba(59, 130, 246, 0.15);
    transform: translateX(2px);
}

/* 暗色主题下的语言选择器选项 - 使用蓝色主题 */
[data-theme="dark"] .language-option.selected {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
}

[data-theme="dark"] .language-option.selected:hover {
    background: rgba(59, 130, 246, 0.3);
}

/* 暗色主题下的语言选择器样式 */
[data-theme="dark"] .language-select {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border-color: rgba(59, 130, 246, 0.5);
    width: 100px; /* 确保暗色主题下宽度保持一致 */
    font-family: 'Twemoji Country Flags', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', system-ui, sans-serif; /* 国旗字体支持 */
}

/* 暗色主题下的语言选择器按钮内emoji样式 */
[data-theme="dark"] .language-select .language-emoji {
    font-family: 'Twemoji Country Flags', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

[data-theme="dark"] .language-select:hover {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.7);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

[data-theme="dark"] .language-select:focus {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.7);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

/* 自动主题下的语言选择器样式 */
[data-theme="auto"] .language-select {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border-color: rgba(59, 130, 246, 0.5);
    width: 100px; /* 确保自动主题下宽度保持一致 */
}

[data-theme="auto"] .language-select:hover {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.7);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

[data-theme="auto"] .language-select:focus {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.7);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

/* 自动主题下的语言选择器选项 - 使用蓝色主题 */
[data-theme="auto"] .language-option.selected {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
}

[data-theme="auto"] .language-option.selected:hover {
    background: rgba(59, 130, 246, 0.3);
}

/* 语言交换按钮 */
.language-swap-btn {
    background: none;
    border: none;
    padding: 6px;
    cursor: pointer;
    border-radius: 4px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px; /* 与控制按钮保持一致的宽度 */
    height: 32px; /* 与控制按钮保持一致的高度 */
    box-sizing: border-box; /* 确保高度计算包含边框和内边距 */
}

.language-swap-btn:hover {
    background-color: var(--background-hover);
    color: var(--text-primary);
}

.language-swap-btn svg {
    width: 16px;
    height: 16px;
}

/* 配置标题按钮 */
.config-header-btn {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.config-header-btn:hover {
    background-color: var(--background-hover);
    color: var(--text-primary);
}

.config-header-btn svg {
    width: 16px;
    height: 16px;
}

/* 识别类型标题需要flex布局来支持右侧按钮 */
.recognition-type-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.recognition-type-header h4 {
    margin: 0;
    flex: 1;
}

/* 大尺寸弹窗 */
.modal-large .modal-content {
    max-width: 600px;
    width: 90%;
}

/* 提示词文本框 */
.prompt-textarea {
    width: 100%;
    min-height: 200px;
    padding: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-primary);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    outline: none;
}

.prompt-textarea:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

/* 提示词帮助信息 */
.prompt-help {
    margin-bottom: 12px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border-left: 3px solid var(--color-primary);
}

.prompt-help p {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: var(--text-secondary);
}

.prompt-help ul {
    margin: 0;
    padding-left: 20px;
}

.prompt-help li {
    margin: 4px 0;
    font-size: 13px;
    color: var(--text-secondary);
}

.prompt-help code {
    background: var(--background-primary);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    color: var(--color-primary);
}

/* 弹窗操作按钮 */
.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-primary);
}

/* 网络代理配置样式 */
.proxy-enable-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.proxy-enable-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.proxy-enable-left label {
    margin: 0;
    font-weight: 500;
    color: var(--text-primary);
}

.proxy-config-form {
    margin-top: 16px;
    transition: all 0.3s ease;
}

.proxy-type-container {
    position: relative;
    display: inline-block;
}

.proxy-type-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    min-width: 120px;
    justify-content: space-between;
    transition: var(--transition-fast);
}

.proxy-type-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

.proxy-type-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    box-shadow: 0 4px 12px var(--shadow-medium);
    z-index: 1000;
    display: none;
    margin-top: 4px;
}

.proxy-type-menu.show {
    display: block;
}

.proxy-type-option {
    padding: 8px 12px;
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 1px solid var(--border-primary);
}

.proxy-type-option:last-child {
    border-bottom: none;
}

.proxy-type-option:hover {
    background: var(--bg-tertiary);
}

.proxy-type-option.selected {
    background: var(--accent-primary);
    color: var(--text-on-accent);
}

.proxy-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 13px;
    transition: var(--transition-fast);
}

.proxy-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px var(--accent-primary-alpha);
}

.proxy-auth-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.proxy-auth-header label {
    margin: 0;
    font-weight: 500;
    color: var(--text-primary);
}

.proxy-auth-fields {
    padding-left: 16px;
    border-left: 2px solid var(--border-primary);
    margin-top: 12px;
}

.proxy-status-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.proxy-status-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 12px;
}

.proxy-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-tertiary);
}

.proxy-status.unknown .status-indicator {
    background: var(--text-tertiary);
}

.proxy-status.testing .status-indicator {
    background: var(--warning-color);
    animation: pulse-fade 1.5s infinite;
}

.proxy-status.connected .status-indicator {
    background: var(--success-color);
}

.proxy-status.failed .status-indicator {
    background: var(--error-color);
}

.proxy-status.unknown .status-text {
    color: var(--text-tertiary);
}

.proxy-status.testing .status-text {
    color: var(--warning-color);
}

.proxy-status.connected .status-text {
    color: var(--success-color);
}

.proxy-status.failed .status-text {
    color: var(--error-color);
}

@keyframes pulse-fade {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.btn-secondary {
    padding: 8px 16px;
    border: 1px solid var(--border-primary);
    background: var(--background-secondary);
    color: var(--text-primary);
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: var(--background-hover);
    border-color: var(--border-hover);
}


/* 翻译模型选择器样式 */
.translate-model-selector {
    display: flex;
    align-items: center;
    gap: 6px;
}

.translate-model-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 0px 8px 10px; /* 单独设置：上 右 下 左，左右间距可独立调整 */
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    height: 32px; /* 固定高度，确保所有按钮高度一致 */
    box-sizing: border-box;
    justify-content: center; /* 内容居中对齐 */
}

.translate-model-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.translate-model-btn.active {
    /* 复刻主页面模型选择的高亮效果 */
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    font-weight: 500;
    border-color: rgba(25, 118, 210, 0.3);
}

.translate-model-btn.active:hover {
    /* 复刻主页面模型选择的选中悬停效果 */
    background: rgba(25, 118, 210, 0.12);
    color: #1976d2;
    border-color: rgba(25, 118, 210, 0.4);
}

.translate-model-btn .model-icon {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

.translate-model-btn .model-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.translate-model-btn .model-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

.translate-result-content {
    flex: 1;
    background: var(--area-result); /* 复刻OCR结果区域背景 */
    border: 1px solid var(--border-primary); /* 复刻OCR结果区域边框 */
    border-radius: 12px; /* 复刻OCR结果区域圆角 */
    padding: 16px; /* 复刻OCR结果区域内边距 */
    font-family: var(--font-family); /* 统一字体族 */
    font-size: var(--font-size-lg); /* 从md提升到lg，与输入框保持一致 */
    line-height: var(--line-height-relaxed); /* 宽松行高，提升可读性 */
    font-weight: var(--font-weight-normal); /* 正常字重 */
    white-space: pre-wrap; /* 保持换行格式 */
    word-wrap: break-word; /* 长单词换行 */
    resize: none; /* 禁止调整大小 */
    width: 100%; /* 确保宽度填满容器 */
    box-sizing: border-box; /* 包含padding和border在内的盒模型 */
    color: var(--text-primary);
    overflow-y: auto; /* 垂直滚动 */
    /* 改善翻译结果显示效果 */
    letter-spacing: 0.01em; /* 轻微增加字间距，提升可读性 */
    /* 隐藏滚动条但保持滚动功能 - 复刻OCR结果区域 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.translate-result-content::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.translate-result-content:focus {
    outline: none;
    border-color: var(--border-secondary); /* 复刻OCR结果区域焦点样式 */
    background: var(--area-result); /* 复刻OCR结果区域焦点样式 */
}

.translate-result-content::placeholder {
    color: var(--text-tertiary);
}

/* 翻译中状态的占位符样式 - 与默认样式保持一致 */
.translate-result-content.translating::placeholder {
    color: var(--text-tertiary);
}

/* 翻译失败状态的占位符样式 - 与默认样式保持一致 */
.translate-result-content.error::placeholder {
    color: var(--text-tertiary);
    font-style: normal;
}

/* 翻译结果区域操作按钮样式 */
.translate-action-btn {
    position: absolute;
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease, opacity 0.3s ease; /* 分离过渡效果 */
    z-index: 15; /* 提高z-index确保在tooltip之上 */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    /* 默认隐藏按钮 */
    opacity: 0;
    pointer-events: none; /* 隐藏时不响应鼠标事件 */
}

/* 翻译结果区域底部检测区域 */
.translate-bottom-hover-area {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px; /* 增加检测区域高度，提升用户体验 */
    pointer-events: auto; /* 允许鼠标事件 */
    z-index: 5; /* 在按钮下方但在文本上方 */
    /* 调试用：可以临时添加背景色查看区域 */
    /* background: rgba(255, 0, 0, 0.1); */
}

/* 只通过JavaScript控制按钮显示，移除CSS hover选择器避免冲突 */
.translate-result-area.show-buttons .translate-action-btn {
    opacity: 1;
    pointer-events: auto;
}

/* 当按钮显示后，悬停在按钮上的样式效果（不影响显示状态） */
.translate-action-btn:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

/* 移除之前的整体悬停规则和::after伪元素 */

.translate-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.translate-action-btn .btn-icon {
    width: 14px;
    height: 14px;
}

/* 清空按钮位置 */
.translate-clear-btn {
    bottom: 16px;
    right: 96px; /* 最左侧位置 */
}

/* 翻译按钮位置 */
.translate-execute-btn {
    bottom: 16px;
    right: 56px; /* 中间位置 */
}

/* 复制按钮位置 */
.translate-copy-btn {
    bottom: 16px;
    right: 16px; /* 最右侧位置 */
}

/* 左下角导航按钮位置 */
.translate-settings-btn {
    bottom: 16px;
    left: 16px;
}

.translate-back-btn {
    bottom: 16px;
    left: 56px; /* 位置在设置按钮右侧 */
}

.translate-history-btn {
    bottom: 16px;
    left: 96px; /* 位置在返回按钮右侧 */
}

.translate-placeholder {
    color: var(--text-tertiary);
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 暗色主题适配 - 翻译页面 */
[data-theme="dark"] .translate-textarea {
    background: var(--area-result) !important;
}

[data-theme="dark"] .translate-textarea:focus {
    background: var(--area-result) !important;
}

[data-theme="dark"] .translate-result-content {
    background: var(--area-result) !important;
}

[data-theme="dark"] .translate-result-content:focus {
    background: var(--area-result) !important;
}

/* 暗色主题下的翻译模型选择器 */
[data-theme="dark"] .translate-model-btn {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .translate-model-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .translate-model-btn.active {
    /* 复刻主页面模型选择在暗色主题下的高亮效果 */
    background: rgba(25, 118, 210, 0.15);
    color: #64b5f6;
    font-weight: 500;
    border-color: rgba(25, 118, 210, 0.3);
}

[data-theme="dark"] .translate-model-btn.active:hover {
    /* 复刻主页面模型选择在暗色主题下的选中悬停效果 */
    background: rgba(25, 118, 210, 0.2);
    color: #64b5f6;
    border-color: rgba(25, 118, 210, 0.4);
}

/* 暗色主题下的模型测试按钮 */
[data-theme="dark"] .model-test-btn {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .model-test-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

/* 暗色主题下的测试状态样式 */
[data-theme="dark"] .model-test-btn.test-status-untested {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
}

[data-theme="dark"] .model-test-btn.test-status-untested:hover {
    background: #4b5563;
    border-color: #4b5563;
    box-shadow: 0 2px 8px rgba(75, 85, 99, 0.4);
}

[data-theme="dark"] .model-test-btn.test-status-success {
    background: rgba(34, 197, 94, 0.15);
    color: #4ade80;
    border-color: rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .model-test-btn.test-status-success:hover {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .model-test-btn.test-status-failed {
    background: rgba(239, 68, 68, 0.15);
    color: #f87171;
    border-color: rgba(239, 68, 68, 0.3);
}

[data-theme="dark"] .model-test-btn.test-status-failed:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 暗色主题下的传统服务商测试按钮 */
[data-theme="dark"] .traditional-test-btn {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .traditional-test-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

/* 暗色主题下的传统服务商测试状态样式 */
[data-theme="dark"] .traditional-test-btn.test-status-untested {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
}

[data-theme="dark"] .traditional-test-btn.test-status-untested:hover {
    background: #4b5563;
    border-color: #4b5563;
    box-shadow: 0 2px 8px rgba(75, 85, 99, 0.4);
}

[data-theme="dark"] .traditional-test-btn.test-status-success {
    background: rgba(34, 197, 94, 0.15);
    color: #4ade80;
    border-color: rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .traditional-test-btn.test-status-success:hover {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .traditional-test-btn.test-status-failed {
    background: rgba(239, 68, 68, 0.15);
    color: #f87171;
    border-color: rgba(239, 68, 68, 0.3);
}

[data-theme="dark"] .traditional-test-btn.test-status-failed:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 暗色主题下的网络代理按钮状态样式 */
[data-theme="dark"] .model-actions .btn-small.proxy-btn-warning {
    background: rgba(217, 119, 6, 0.15) !important;
    color: #f59e0b !important;
    border-color: rgba(217, 119, 6, 0.3) !important;
}

[data-theme="dark"] .model-actions .btn-small.proxy-btn-warning:hover {
    background: rgba(217, 119, 6, 0.2) !important;
    border-color: rgba(217, 119, 6, 0.4) !important;
    box-shadow: 0 2px 8px rgba(217, 119, 6, 0.3);
}

[data-theme="dark"] .model-actions .btn-small.proxy-btn-success {
    background: rgba(34, 197, 94, 0.15) !important;
    color: #4ade80 !important;
    border-color: rgba(34, 197, 94, 0.3) !important;
}

[data-theme="dark"] .model-actions .btn-small.proxy-btn-success:hover {
    background: rgba(34, 197, 94, 0.2) !important;
    border-color: rgba(34, 197, 94, 0.4) !important;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .model-actions .btn-small.proxy-btn-error {
    background: rgba(239, 68, 68, 0.15) !important;
    color: #f87171 !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
}

[data-theme="dark"] .model-actions .btn-small.proxy-btn-error:hover {
    background: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.4) !important;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 翻译加载状态 */
.translate-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--text-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: var(--text-secondary);
}

/* 翻译结果样式 */
.translate-result {
    color: var(--text-primary);
    line-height: 1.6;
}

.translate-result p {
    margin: 0 0 12px 0;
}

.translate-result p:last-child {
    margin-bottom: 0;
}

/* 全局隐藏滚动条但保持滚动功能 */
* {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

*::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}



.translate-model-select-icon svg {
    width: 16px;
    height: 16px;
}

/* 功能图标样式 */
.capability-icons {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-left: 8px;
}

.capability-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.capability-icon {
    width: 16px;
    height: 16px;
    stroke-width: 2;
    transition: all 0.2s ease;
}

/* 功能图标颜色定义 */
.capability-icon-wrapper[data-capability="text"] {
    color: #3b82f6 !important; /* 蓝色 - 文本能力 */
}

.capability-icon-wrapper[data-capability="vision"] {
    color: #10b981 !important; /* 绿色 - 视觉能力 */
}

.capability-icon-wrapper[data-capability="reasoning"] {
    color: #8b5cf6 !important; /* 紫色 - 推理能力 */
}

/* 暗色主题下的功能图标颜色调整 */
[data-theme="dark"] .capability-icon-wrapper[data-capability="text"] {
    color: #60a5fa !important; /* 亮蓝色 */
}

[data-theme="dark"] .capability-icon-wrapper[data-capability="vision"] {
    color: #34d399 !important; /* 亮绿色 */
}

[data-theme="dark"] .capability-icon-wrapper[data-capability="reasoning"] {
    color: #a78bfa !important; /* 亮紫色 */
}



/* 已添加模型的功能图标在高亮状态下的颜色调整 */
.fetch-model-item.added .capability-icon-wrapper[data-capability="text"] {
    color: #1e40af !important;
}

.fetch-model-item.added .capability-icon-wrapper[data-capability="vision"] {
    color: #047857 !important;
}

.fetch-model-item.added .capability-icon-wrapper[data-capability="reasoning"] {
    color: #6d28d9 !important;
}

/* 暗色主题下已添加模型的功能图标颜色 */
[data-theme="dark"] .fetch-model-item.added .capability-icon-wrapper[data-capability="text"] {
    color: #93c5fd !important;
}

[data-theme="dark"] .fetch-model-item.added .capability-icon-wrapper[data-capability="vision"] {
    color: #6ee7b7 !important;
}

[data-theme="dark"] .fetch-model-item.added .capability-icon-wrapper[data-capability="reasoning"] {
    color: #c4b5fd !important;
}



.translate-model-select-name {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
}

/* 拖拽排序样式 */
.model-item {
    position: relative;
    transition: transform 0.25s ease-out;
}

.model-item.dragging {
    transition: none !important;
    cursor: grabbing !important;
}



.drag-placeholder {
    height: 48px;
    margin-bottom: 8px;
    display: none;
    align-items: center;
    justify-content: center;
}

/* 拖拽时显示占位符 */
.drag-placeholder.show {
    display: flex;
}

.drag-placeholder-content {
    color: var(--text-secondary);
    font-size: 12px;
    font-style: italic;
}

/* 拖拽时的视觉反馈 */
.model-list[data-sortable="true"] .model-item {
    cursor: grab;
}

/* 服务商列表拖拽样式 */
.service-list[data-sortable="true"] .service-item {
    cursor: grab;
}

.service-item.dragging {
    transition: none !important;
    cursor: grabbing !important;
}

/* 确保拖拽时服务商图标保持正常显示 */
.service-item.dragging .service-icon {
    pointer-events: none;
    transform: none !important;
}

.model-list[data-sortable="true"] .model-item:active {
    cursor: grabbing;
}



/* 确保拖拽时按钮仍然可点击 */
.model-item .model-delete-btn,
.model-item .model-test-btn {
    cursor: pointer !important;
    pointer-events: auto !important;
}

/* 确保拖拽时图标保持正常显示 */
.model-item.dragging .model-icon svg {
    pointer-events: none;
    transform: none !important;
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }

    /* 高分辨率下的字体微调 */
    .result-text, .raw-result-text {
        font-weight: 400;
        letter-spacing: 0.01em;
    }
}

/* 用户偏好设置支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 用户字体大小偏好支持 */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-primary: #666666;
    }

    [data-theme="dark"] {
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-primary: #999999;
    }
}

/* 长文本优化 - 针对OCR和翻译结果的特殊处理 */
.result-text,
.raw-result-text,
.translate-textarea,
.translate-result-content,
.translate-dual-pane .result-text,
#ocr-result-area .result-text {
    /* 改善长文本阅读体验 */
    text-align: left;
    word-break: break-word;
    hyphens: auto; /* 自动断词 */
    -webkit-hyphens: auto;
    -moz-hyphens: auto;
    -ms-hyphens: auto;
}

/* 针对不同内容类型的字体微调 */
.result-text[data-content-type="code"],
.raw-result-text {
    font-family: var(--font-mono);
    letter-spacing: 0.02em;
    tab-size: 4; /* 设置制表符宽度 */
}

.result-text[data-content-type="formula"] {
    font-family: var(--font-serif);
    text-align: center;
}

.result-text[data-content-type="table"] {
    font-family: var(--font-family);
    white-space: pre;
    overflow-x: auto;
}

/* 临时隐藏网络代理功能 - 便于后续重新启用 */
#openai-proxy-btn,
#anthropic-proxy-btn,
#google-proxy-btn {
    display: none !important;
}

/* 个人中心样式 */
.user-profile-section {
    display: flex;
    gap: 24px;
    align-items: flex-start;
    justify-content: space-between;
}

.user-avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    border: 2px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: var(--transition-fast);
}

.user-avatar.clickable {
    cursor: pointer;
}

.user-avatar:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px var(--primary-color-alpha);
}

.user-avatar.clickable:hover {
    transform: scale(1.05);
}

.user-avatar svg {
    width: 30px;
    height: 30px;
    color: var(--text-secondary);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 用户名容器样式 */
.user-name-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

input.user-name-input {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-primary) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: var(--text-primary) !important;
    text-align: center !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    transition: var(--transition-fast) !important;
    width: 100px !important;
    cursor: pointer !important;
}

input.user-name-input:focus {
    outline: none !important;
    background: var(--bg-secondary) !important;
    border: 1px solid var(--primary-color) !important;
    box-shadow: 0 0 0 2px var(--primary-color-alpha) !important;
    cursor: text !important;
}

input.user-name-input[readonly] {
    cursor: pointer !important;
}

input.user-name-input:not([readonly]):hover {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-secondary) !important;
    box-shadow: 0 1px 3px var(--shadow-light) !important;
}

/* 右侧功能区域样式 */
.user-functions-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 200px;
}

/* 使用额度进度条样式 */
.usage-progress-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
}

.progress-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    font-family: var(--font-mono);
}

/* 个人中心额度进度条容器 */
.usage-progress-item .progress-bar {
    width: 100%;
    height: 12px;
    background: var(--bg-tertiary);
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 个人中心额度进度条填充 */
.usage-progress-item .progress-fill {
    height: 100%;
    /* 背景色由JavaScript动态设置 */
    background: transparent;
    border-radius: 5px;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
}

/* 个人中心额度进度条闪光动画 */
.usage-progress-item .progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        transparent 30%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 70%,
        transparent 100%
    );
    animation: progress-shine 2.5s ease-in-out infinite;
    border-radius: 5px;
    pointer-events: none;
}

@keyframes progress-shine {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    10% {
        opacity: 0.8;
    }
    90% {
        opacity: 0.8;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 备选实现：使用mask来精确控制动画区域 */
.progress-fill-v2 {
    height: 100%;
    background: linear-gradient(90deg, #1976d2, #42a5f5);
    border-radius: 5px;
    transition: width 0.5s ease;
    position: relative;
    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.3);
    overflow: hidden;
}

.progress-fill-v2::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        transparent 40%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 60%,
        transparent 100%
    );
    animation: progress-shine-v2 2s ease-in-out infinite;
    border-radius: 5px;
    pointer-events: none;
}

@keyframes progress-shine-v2 {
    0% {
        transform: translateX(-50px);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        transform: translateX(calc(100% + 50px));
        opacity: 0;
    }
}

/* 彩蛋标题样式 - 保持隐蔽性，无视觉反馈 */
#ocr-quota-title,
#translate-quota-title {
    cursor: default; /* 使用默认箭头光标，不显示点击提示 */
    user-select: none;
}

/* 确认对话框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.reset-modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 8px 32px var(--shadow-heavy);
    border: 1px solid var(--border-primary);
    min-width: 280px;
    max-width: 400px;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-close svg {
    width: 16px;
    height: 16px;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin: 0;
    color: var(--text-primary);
    line-height: 1.5;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--border-primary);
    background: var(--bg-secondary);
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    border: 1px solid transparent;
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    border-color: var(--border-secondary);
}

.btn-primary {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
    border-color: var(--border-primary);
}

.btn-primary:hover {
    background: var(--primary-btn-hover);
    border-color: var(--border-secondary);
}

.btn-danger {
    background: var(--error-text);
    color: white;
    border-color: var(--error-text);
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}



/* 用户信息标题样式 */
.recognition-type-header {
    position: relative;
}

/* 隐藏重置功能的标题样式 - 不显示任何交互提示 */
#user-info-title {
    user-select: none; /* 防止文本选择 */
}



/* 使用统计样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.stat-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: var(--transition-fast);
}

.stat-item:hover {
    background: var(--accent-hover);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}



/* OCR Pro服务说明样式 */
.ocrpro-service-notice {
    margin-top: 12px;
    padding: 12px;
    background: var(--info-bg);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    border-left: 3px solid var(--info-text);
}

.ocrpro-service-notice p {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.ocrpro-service-notice p:last-child {
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .user-profile-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 16px;
    }

    .user-functions-area {
        width: 100%;
        min-width: auto;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .user-name-container {
        justify-content: center;
    }

    .usage-progress-item {
        margin-bottom: 8px;
    }
}

/* 数据备份与恢复样式 */
.backup-options {
    margin-bottom: 16px;
    display: grid;
    grid-template-columns: 1fr 1fr; /* 两列等宽布局 */
    gap: 12px 16px; /* 行间距12px，列间距16px */
}

.backup-option-item {
    /* 移除margin-bottom，由grid gap控制间距 */
}

.backup-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 4px;
}

.backup-checkbox-label input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.backup-checkbox-text {
    font-weight: 500;
    color: var(--text-primary);
}

.backup-option-desc {
    font-size: 12px;
    color: var(--text-secondary);
    margin-left: 24px;
    line-height: 1.4;
}

.backup-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.backup-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    min-height: 40px;
}

.backup-btn.primary {
    background: var(--primary-btn-bg);
    color: var(--primary-btn-text);
}

.backup-btn.primary:hover {
    background: var(--primary-btn-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.backup-btn.secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.backup-btn.secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--border-secondary);
}

.backup-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.backup-btn .btn-icon {
    width: 16px;
    height: 16px;
}

.backup-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--bg-secondary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-btn-bg);
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 12px;
    color: var(--text-secondary);
    text-align: center;
}

/* 恢复文件区域样式 */
.restore-file-area {
    margin-bottom: 12px;
}

/* 数据状态刷新按钮样式 */
.data-status-refresh-btn {
    background: none;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 6px 8px;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    color: var(--text-secondary);
}

.data-status-refresh-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.data-status-refresh-btn .btn-icon {
    width: 16px;
    height: 16px;
}

.data-status-refresh-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 数据分类样式 */
.data-summary-categories {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.data-category-item {
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: var(--transition-fast);
    background: var(--bg-primary);
}

.data-category-item:hover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
}

.data-category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 24px; /* 确保一致的行高 */
}

.data-category-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
    margin-right: 16px; /* 确保标题和状态之间有足够间距 */
}

.data-category-status {
    display: flex;
    align-items: center;
    gap: 16px; /* 增加间距 */
    font-size: 14px;
    color: var(--text-secondary);
    min-width: 120px; /* 确保状态区域有足够宽度 */
    justify-content: flex-end; /* 右对齐内容 */
}

.data-category-count {
    background: var(--primary-color-alpha);
    color: var(--primary-color);
    padding: 2px 6px; /* 调整左右内边距 */
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    width: 44px; /* 设置固定宽度，能容纳"10/12"等较长数量 */
    text-align: center; /* 居中显示数字 */
    flex-shrink: 0; /* 防止收缩 */
    display: inline-block; /* 确保宽度设置生效 */
    white-space: nowrap; /* 防止文字换行 */
}

.data-category-size {
    color: var(--text-tertiary);
    font-size: 12px;
    min-width: 48px; /* 固定最小宽度确保对齐 */
    text-align: right; /* 右对齐数据大小 */
    flex-shrink: 0; /* 防止收缩 */
}

/* 数据详情弹窗样式 */
.data-detail-modal-content {
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
}

.data-detail-content {
    max-height: 60vh;
    overflow-y: auto;
}

.data-detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
    min-height: 40px; /* 确保一致的行高 */
}

.data-detail-item:last-child {
    border-bottom: none;
}

.data-detail-item-name {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
    margin-right: 16px; /* 确保名称和状态之间有足够间距 */
    word-break: break-word; /* 处理长名称的换行 */
}

.data-detail-item-status {
    display: flex;
    align-items: center;
    gap: 16px; /* 增加间距 */
    min-width: 140px; /* 确保状态区域有足够宽度 */
    justify-content: flex-end; /* 右对齐内容 */
}

.data-detail-status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    min-width: 40px; /* 固定最小宽度确保对齐 */
    text-align: center; /* 居中显示状态文字 */
    flex-shrink: 0; /* 防止收缩 */
}

.data-detail-status-badge.exists {
    background: var(--success-color-alpha);
    color: var(--success-color);
}

.data-detail-status-badge.missing {
    background: var(--warning-color-alpha);
    color: var(--warning-color);
}

.data-detail-status-badge.error {
    background: var(--error-color-alpha);
    color: var(--error-color);
}

.data-detail-size {
    color: var(--text-secondary);
    font-size: 12px;
    min-width: 60px; /* 固定最小宽度确保对齐 */
    text-align: right; /* 右对齐数据大小 */
    flex-shrink: 0; /* 防止收缩 */
}

.restore-drop-zone {
    border: 2px dashed var(--border-primary);
    border-radius: 12px;
    padding: 20px 16px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
    background: var(--bg-secondary);
}

.restore-drop-zone:hover,
.restore-drop-zone.drag-over {
    border-color: var(--primary-btn-bg);
    background: var(--primary-color-alpha);
}

.drop-icon {
    width: 40px;
    height: 40px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.drop-text {
    color: var(--text-secondary);
}

.drop-main-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
}

.drop-sub-text {
    font-size: 14px;
}

.drop-link {
    color: var(--primary-btn-bg);
    cursor: pointer;
    text-decoration: underline;
}

.drop-link:hover {
    color: var(--primary-btn-hover);
}

.restore-file-info {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
}

.file-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.file-remove-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: var(--transition-fast);
}

.file-remove-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.file-details {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.restore-options {
    margin-bottom: 16px;
}

.restore-option-item {
    margin-bottom: 12px;
}

.restore-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 数据状态摘要样式 */
.data-summary {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.summary-loading {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

.summary-section {
    margin-bottom: 16px;
}

.summary-section:last-child {
    margin-bottom: 0;
}

.summary-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 14px;
}

.summary-items {
    display: grid;
    gap: 8px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    background: var(--bg-tertiary);
    border-radius: 6px;
    font-size: 12px;
}

.summary-item-name {
    color: var(--text-primary);
    font-weight: 500;
}

.summary-item-value {
    color: var(--text-secondary);
}

.summary-item.exists .summary-item-value {
    color: var(--success-text);
}

.summary-item.missing .summary-item-value {
    color: var(--text-secondary);
}

.summary-item.error .summary-item-value {
    color: var(--error-text);
}
